// src/components/student-management/add-student-wizard-new.tsx
'use client';

import { useEffect, useState } from 'react';
import { useMasterData } from '../../hooks/useMasterData';
import { useStudents } from '../../hooks/useStudents';
import { MasterDataService } from '../../services/masterDataService';
import { CreateStudentData } from '../../services/studentService';

interface AddStudentWizardProps {
  onClose: () => void;
  onSuccess?: (message: string) => void;
  onError?: (error: string) => void;
}

const AddStudentWizard = ({ onClose, onSuccess, onError }: AddStudentWizardProps) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [rollNumberError, setRollNumberError] = useState<string | null>(null);

  // Get master data
  const { data: masterData, loading: masterDataLoading, error: masterDataError } = useMasterData();
  const { createStudent } = useStudents();

  const [formData, setFormData] = useState({
    // Personal Info
    firstName: '',
    lastName: '',
    dateOfBirth: '',
    gender: '',
    email: '',
    phoneNumber: '',
    address: '',
    
    // Guardian Details
    guardianName: '',
    guardianRelationId: '',
    guardianPhone: '',
    guardianEmail: '',
    guardianAddress: '',
    emergencyContact: '',
    
    // Academic Info
    classId: '',
    sectionId: '',
    rollNumber: '',
    previousSchool: '',
    academicYearId: '',
    
    // Documents (File objects for upload)
    birthCertificate: null as File | null,
    previousRecords: null as File | null,
    medicalRecords: null as File | null,
    photograph: null as File | null
  });  // Auto-select current academic year
  useEffect(() => {
    const currentYear = masterData?.currentAcademicYear;
    if (currentYear && !formData.academicYearId) {
      setFormData(prev => ({
        ...prev,
        academicYearId: currentYear.id
      }));
    }
  }, [masterData, formData.academicYearId]);

  const steps = [
    { number: 1, title: 'Personal Info', description: 'Basic student information' },
    { number: 2, title: 'Guardian Details', description: 'Parent/Guardian information' },
    { number: 3, title: 'Academic Info', description: 'Class and academic details' },
    { number: 4, title: 'Documents', description: 'Upload required documents' }
  ];

  const handleInputChange = (field: string, value: string | File | null) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear roll number error when relevant fields change
    if (['rollNumber', 'classId', 'sectionId', 'academicYearId'].includes(field)) {
      setRollNumberError(null);
    }
  };

  // Check roll number availability
  const checkRollNumber = async () => {
    if (!formData.rollNumber || !formData.classId || !formData.sectionId || !formData.academicYearId) {
      return true;
    }

    try {
      const exists = await MasterDataService.isRollNumberExists(
        formData.rollNumber,
        formData.classId,
        formData.sectionId,
        formData.academicYearId
      );

      if (exists) {
        setRollNumberError('This roll number already exists for the selected class, section, and academic year');
        return false;
      }

      setRollNumberError(null);
      return true;
    } catch (error) {
      console.error('Error checking roll number:', error);
      setRollNumberError('Error checking roll number availability');
      return false;
    }
  };

  const validateStep = async (step: number): Promise<boolean> => {
    switch (step) {
      case 1:
        return !!(formData.firstName && formData.lastName && formData.dateOfBirth && formData.gender);
      case 2:
        return !!(formData.guardianName && formData.guardianRelationId && formData.guardianPhone);
      case 3:
        const rollNumberValid = await checkRollNumber();
        return !!(formData.classId && formData.sectionId && formData.rollNumber && formData.academicYearId) && rollNumberValid;
      case 4:
        return true; // Documents are optional
      default:
        return false;
    }
  };

  const handleNext = async () => {
    const isValid = await validateStep(currentStep);
    if (isValid && currentStep < 4) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = async () => {
    const isValid = await validateStep(currentStep);
    if (!isValid) return;

    setIsSubmitting(true);
    try {
      // TODO: Upload files to Supabase Storage and get URLs
      // For now, we'll submit without file URLs
      const studentData: CreateStudentData = {
        firstName: formData.firstName,
        lastName: formData.lastName,
        dateOfBirth: formData.dateOfBirth,
        gender: formData.gender as 'male' | 'female' | 'other',
        email: formData.email || undefined,
        phoneNumber: formData.phoneNumber || undefined,
        address: formData.address || undefined,
        guardianName: formData.guardianName,
        guardianRelationId: formData.guardianRelationId,
        guardianPhone: formData.guardianPhone,
        guardianEmail: formData.guardianEmail || undefined,
        guardianAddress: formData.guardianAddress || undefined,
        emergencyContact: formData.emergencyContact || undefined,
        classId: formData.classId,
        sectionId: formData.sectionId,
        rollNumber: formData.rollNumber,
        previousSchool: formData.previousSchool || undefined,
        academicYearId: formData.academicYearId,
        // TODO: Add file URLs after implementing file upload
        birthCertificateUrl: undefined,
        previousRecordsUrl: undefined,
        medicalRecordsUrl: undefined,
        photographUrl: undefined
      };

      await createStudent(studentData);
      onSuccess?.('Student created successfully!');
      onClose();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create student';
      onError?.(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleFileChange = (field: string, file: File | null) => {
    setFormData(prev => ({ ...prev, [field]: file }));
  };

  if (masterDataLoading) {
    return (
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
        <div className="bg-white rounded-2xl p-8">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
            <span className="ml-3 text-gray-700">Loading master data...</span>
          </div>
        </div>
      </div>
    );
  }

  if (masterDataError) {
    return (
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
        <div className="bg-white rounded-2xl p-8 max-w-md">
          <div className="text-center">
            <div className="text-red-600 mb-4">
              <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 19c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Error Loading Data</h3>
            <p className="text-gray-600 mb-4">{masterDataError}</p>
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    );
  }

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="block text-base-app font-semibold text-gray-800">First Name *</label>
                <input
                  type="text"
                  value={formData.firstName}
                  onChange={(e) => handleInputChange('firstName', e.target.value)}
                  placeholder="Enter first name"
                  className="w-full px-4 py-3 border border-gray-200 rounded-xl bg-gray-50/50 focus:bg-white focus:ring-2 focus:ring-indigo-500/20 focus:border-indigo-500 transition-all duration-200 text-base-app font-medium placeholder:text-gray-400 shadow-sm hover:shadow-md"
                  required
                />
              </div>
              
              <div className="space-y-2">
                <label className="block text-base-app font-semibold text-gray-800">Last Name *</label>
                <input
                  type="text"
                  value={formData.lastName}
                  onChange={(e) => handleInputChange('lastName', e.target.value)}
                  placeholder="Enter last name"
                  className="w-full px-4 py-3 border border-gray-200 rounded-xl bg-gray-50/50 focus:bg-white focus:ring-2 focus:ring-indigo-500/20 focus:border-indigo-500 transition-all duration-200 text-base-app font-medium placeholder:text-gray-400 shadow-sm hover:shadow-md"
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="block text-base-app font-semibold text-gray-800">Date of Birth *</label>
                <input
                  type="date"
                  value={formData.dateOfBirth}
                  onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-200 rounded-xl bg-gray-50/50 focus:bg-white focus:ring-2 focus:ring-indigo-500/20 focus:border-indigo-500 transition-all duration-200 text-sm font-medium shadow-sm hover:shadow-md"
                  required
                />
              </div>
              
              <div className="space-y-2">
                <label className="block text-base-app font-semibold text-gray-800">Gender *</label>
                <select
                  value={formData.gender}
                  onChange={(e) => handleInputChange('gender', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-200 rounded-xl bg-gray-50/50 focus:bg-white focus:ring-2 focus:ring-indigo-500/20 focus:border-indigo-500 transition-all duration-200 text-sm font-medium shadow-sm hover:shadow-md"
                  required
                >
                  <option value="">Select Gender</option>
                  <option value="male">Male</option>
                  <option value="female">Female</option>
                  <option value="other">Other</option>
                </select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="block text-base-app font-semibold text-gray-800">Email Address</label>
                <input
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  placeholder="Enter email address"
                  className="w-full px-4 py-3 border border-gray-200 rounded-xl bg-gray-50/50 focus:bg-white focus:ring-2 focus:ring-indigo-500/20 focus:border-indigo-500 transition-all duration-200 text-base-app font-medium placeholder:text-gray-400 shadow-sm hover:shadow-md"
                />
              </div>
              
              <div className="space-y-2">
                <label className="block text-base-app font-semibold text-gray-800">Phone Number</label>
                <input
                  type="tel"
                  value={formData.phoneNumber}
                  onChange={(e) => handleInputChange('phoneNumber', e.target.value)}
                  placeholder="Enter phone number"
                  className="w-full px-4 py-3 border border-gray-200 rounded-xl bg-gray-50/50 focus:bg-white focus:ring-2 focus:ring-indigo-500/20 focus:border-indigo-500 transition-all duration-200 text-base-app font-medium placeholder:text-gray-400 shadow-sm hover:shadow-md"
                />
              </div>
            </div>

            <div className="space-y-2">
              <label className="block text-base-app font-semibold text-gray-800">Address</label>
              <textarea
                value={formData.address}
                onChange={(e) => handleInputChange('address', e.target.value)}
                placeholder="Enter current address"
                rows={3}
                className="w-full px-4 py-3 border border-gray-200 rounded-xl bg-gray-50/50 focus:bg-white focus:ring-2 focus:ring-indigo-500/20 focus:border-indigo-500 transition-all duration-200 resize-none text-base-app font-medium placeholder:text-gray-400 shadow-sm hover:shadow-md"
              />
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="block text-base-app font-semibold text-gray-800">Guardian Name *</label>
                <input
                  type="text"
                  value={formData.guardianName}
                  onChange={(e) => handleInputChange('guardianName', e.target.value)}
                  placeholder="Enter guardian name"
                  className="w-full px-4 py-3 border border-gray-200 rounded-xl bg-gray-50/50 focus:bg-white focus:ring-2 focus:ring-indigo-500/20 focus:border-indigo-500 transition-all duration-200 text-sm font-medium placeholder:text-gray-400 shadow-sm hover:shadow-md"
                  required
                />
              </div>
              
              <div className="space-y-2">
                <label className="block text-base-app font-semibold text-gray-800">Relation *</label>
                <select
                  value={formData.guardianRelationId}
                  onChange={(e) => handleInputChange('guardianRelationId', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-200 rounded-xl bg-gray-50/50 focus:bg-white focus:ring-2 focus:ring-indigo-500/20 focus:border-indigo-500 transition-all duration-200 text-sm font-medium shadow-sm hover:shadow-md"
                  required
                >
                  <option value="">Select Relation</option>
                  {masterData?.guardianRelations.map((relation) => (
                    <option key={relation.id} value={relation.id}>
                      {relation.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="block text-base-app font-semibold text-gray-800">Guardian Phone *</label>
                <input
                  type="tel"
                  value={formData.guardianPhone}
                  onChange={(e) => handleInputChange('guardianPhone', e.target.value)}
                  placeholder="Enter guardian phone"
                  className="w-full px-4 py-3 border border-gray-200 rounded-xl bg-gray-50/50 focus:bg-white focus:ring-2 focus:ring-indigo-500/20 focus:border-indigo-500 transition-all duration-200 text-sm font-medium placeholder:text-gray-400 shadow-sm hover:shadow-md"
                  required
                />
              </div>
              
              <div className="space-y-2">
                <label className="block text-base-app font-semibold text-gray-800">Guardian Email</label>
                <input
                  type="email"
                  value={formData.guardianEmail}
                  onChange={(e) => handleInputChange('guardianEmail', e.target.value)}
                  placeholder="Enter guardian email"
                  className="w-full px-4 py-3 border border-gray-200 rounded-xl bg-gray-50/50 focus:bg-white focus:ring-2 focus:ring-indigo-500/20 focus:border-indigo-500 transition-all duration-200 text-sm font-medium placeholder:text-gray-400 shadow-sm hover:shadow-md"
                />
              </div>
            </div>

            <div className="space-y-2">
              <label className="block text-base-app font-semibold text-gray-800">Guardian Address</label>
              <textarea
                value={formData.guardianAddress}
                onChange={(e) => handleInputChange('guardianAddress', e.target.value)}
                placeholder="Enter guardian address"
                rows={3}
                className="w-full px-4 py-3 border border-gray-200 rounded-xl bg-gray-50/50 focus:bg-white focus:ring-2 focus:ring-indigo-500/20 focus:border-indigo-500 transition-all duration-200 resize-none text-sm font-medium placeholder:text-gray-400 shadow-sm hover:shadow-md"
              />
            </div>

            <div className="space-y-2">
              <label className="block text-base-app font-semibold text-gray-800">Emergency Contact</label>
              <input
                type="tel"
                value={formData.emergencyContact}
                onChange={(e) => handleInputChange('emergencyContact', e.target.value)}
                placeholder="Enter emergency contact number"
                className="w-full px-4 py-3 border border-gray-200 rounded-xl bg-gray-50/50 focus:bg-white focus:ring-2 focus:ring-indigo-500/20 focus:border-indigo-500 transition-all duration-200 text-sm font-medium placeholder:text-gray-400 shadow-sm hover:shadow-md"
              />
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="block text-base-app font-semibold text-gray-800">Class *</label>
                <select
                  value={formData.classId}
                  onChange={(e) => handleInputChange('classId', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-200 rounded-xl bg-gray-50/50 focus:bg-white focus:ring-2 focus:ring-indigo-500/20 focus:border-indigo-500 transition-all duration-200 text-sm font-medium shadow-sm hover:shadow-md"
                  required
                >
                  <option value="">Select Class</option>
                  {masterData?.classes.map((cls) => (
                    <option key={cls.id} value={cls.id}>
                      {cls.name}
                    </option>
                  ))}
                </select>
              </div>
              
              <div className="space-y-2">
                <label className="block text-base-app font-semibold text-gray-800">Section *</label>
                <select
                  value={formData.sectionId}
                  onChange={(e) => handleInputChange('sectionId', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-200 rounded-xl bg-gray-50/50 focus:bg-white focus:ring-2 focus:ring-indigo-500/20 focus:border-indigo-500 transition-all duration-200 text-sm font-medium shadow-sm hover:shadow-md"
                  required
                >
                  <option value="">Select Section</option>
                  {masterData?.sections.map((section) => (
                    <option key={section.id} value={section.id}>
                      {section.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="block text-base-app font-semibold text-gray-800">Roll Number *</label>
                <input
                  type="text"
                  value={formData.rollNumber}
                  onChange={(e) => handleInputChange('rollNumber', e.target.value)}
                  placeholder="Enter roll number"
                  className={`w-full px-4 py-3 border rounded-xl bg-gray-50/50 focus:bg-white focus:ring-2 transition-all duration-200 text-sm font-medium placeholder:text-gray-400 shadow-sm hover:shadow-md ${
                    rollNumberError 
                      ? 'border-red-300 focus:ring-red-500/20 focus:border-red-500' 
                      : 'border-gray-200 focus:ring-indigo-500/20 focus:border-indigo-500'
                  }`}
                  required
                />
                {rollNumberError && (
                  <p className="text-sm text-red-600">{rollNumberError}</p>
                )}
              </div>
              
              <div className="space-y-2">
                <label className="block text-base-app font-semibold text-gray-800">Academic Year *</label>
                <select
                  value={formData.academicYearId}
                  onChange={(e) => handleInputChange('academicYearId', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-200 rounded-xl bg-gray-50/50 focus:bg-white focus:ring-2 focus:ring-indigo-500/20 focus:border-indigo-500 transition-all duration-200 text-sm font-medium shadow-sm hover:shadow-md"
                  required
                >
                  <option value="">Select Academic Year</option>
                  {masterData?.academicYears.map((year) => (
                    <option key={year.id} value={year.id}>
                      {year.year}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="space-y-2">
              <label className="block text-base-app font-semibold text-gray-800">Previous School</label>
              <input
                type="text"
                value={formData.previousSchool}
                onChange={(e) => handleInputChange('previousSchool', e.target.value)}
                placeholder="Enter previous school name"
                className="w-full px-4 py-3 border border-gray-200 rounded-xl bg-gray-50/50 focus:bg-white focus:ring-2 focus:ring-indigo-500/20 focus:border-indigo-500 transition-all duration-200 text-sm font-medium placeholder:text-gray-400 shadow-sm hover:shadow-md"
              />
            </div>
          </div>
        );

      case 4:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <p className="text-gray-600">Upload required documents (all documents are optional but recommended)</p>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <FileUploadField
                label="Birth Certificate"
                file={formData.birthCertificate}
                onChange={(file) => handleFileChange('birthCertificate', file)}
                required={false}
              />

              <FileUploadField
                label="Previous Records"
                file={formData.previousRecords}
                onChange={(file) => handleFileChange('previousRecords', file)}
                required={false}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <FileUploadField
                label="Medical Records"
                file={formData.medicalRecords}
                onChange={(file) => handleFileChange('medicalRecords', file)}
                required={false}
              />

              <FileUploadField
                label="Photograph"
                file={formData.photograph}
                onChange={(file) => handleFileChange('photograph', file)}
                required={false}
              />
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden shadow-2xl">
        {/* Header */}
        <div className="bg-gradient-to-r from-indigo-600 to-blue-600 p-6 text-white">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold">Add New Student</h2>
            <button
              onClick={onClose}
              className="text-white/80 hover:text-white transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Step indicators */}
          <div className="flex items-center justify-between mt-6">
            {steps.map((step, index) => (
              <div key={step.number} className="flex items-center">
                <div className={`w-10 h-10 rounded-full flex items-center justify-center font-semibold ${
                  currentStep >= step.number 
                    ? 'bg-white text-indigo-600' 
                    : 'bg-white/20 text-white/60'
                }`}>
                  {step.number}
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium">{step.title}</p>
                  <p className="text-xs text-white/80">{step.description}</p>
                </div>
                {index < steps.length - 1 && (
                  <div className={`ml-8 w-12 h-0.5 ${
                    currentStep > step.number ? 'bg-white' : 'bg-white/20'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {renderStepContent()}
        </div>

        {/* Footer */}
        <div className="border-t border-gray-200 p-6 bg-gray-50">
          <div className="flex items-center justify-between">
            <button
              onClick={handlePrevious}
              disabled={currentStep === 1}
              className="px-6 py-3 border border-gray-300 rounded-xl text-gray-700 font-medium hover:bg-gray-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>

            <div className="flex items-center space-x-3">
              {currentStep < 4 ? (
                <button
                  onClick={handleNext}
                  className="px-8 py-3 bg-gradient-to-r from-indigo-600 to-blue-600 text-white font-semibold rounded-xl hover:from-indigo-700 hover:to-blue-700 transition-all duration-200 shadow-md hover:shadow-lg"
                >
                  Next
                </button>
              ) : (
                <button
                  onClick={handleSubmit}
                  disabled={isSubmitting}
                  className="px-8 py-3 bg-gradient-to-r from-green-600 to-emerald-600 text-white font-semibold rounded-xl hover:from-green-700 hover:to-emerald-700 transition-all duration-200 shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSubmitting ? (
                    <span className="flex items-center">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Creating...
                    </span>
                  ) : (
                    'Create Student'
                  )}
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// File upload component
interface FileUploadFieldProps {
  label: string;
  file: File | null;
  onChange: (file: File | null) => void;
  required?: boolean;
}

const FileUploadField = ({ label, file, onChange, required = false }: FileUploadFieldProps) => {
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0] || null;
    onChange(selectedFile);
  };

  return (
    <div className="space-y-2">
      <label className="block text-base-app font-semibold text-gray-800">
        {label} {required && '*'}
      </label>
      <div className="border-2 border-dashed border-gray-200 rounded-xl p-6 text-center hover:border-indigo-400 hover:bg-indigo-50/30 transition-all duration-200 cursor-pointer group bg-gray-50/30">
        <input 
          type="file" 
          className="sr-only" 
          accept=".pdf,.jpg,.jpeg,.png"
          onChange={handleFileSelect}
          id={`file-${label.replace(/\s+/g, '-').toLowerCase()}`}
        />
        <label 
          htmlFor={`file-${label.replace(/\s+/g, '-').toLowerCase()}`}
          className="cursor-pointer"
        >
          <div className="flex flex-col items-center space-y-3">
            <div className="w-10 h-10 bg-indigo-100 rounded-xl flex items-center justify-center group-hover:bg-indigo-200 transition-colors">
              <svg className="w-5 h-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
              </svg>
            </div>
            {file ? (
              <div className="text-center">
                <p className="text-sm font-medium text-green-600">{file.name}</p>
                <p className="text-xs text-gray-500">Click to change</p>
              </div>
            ) : (
              <>
                <span className="inline-flex items-center px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-lg hover:bg-indigo-700 transition-colors shadow-md">
                  Choose File
                </span>
                <p className="text-xs text-gray-500 font-medium">PDF, JPG, PNG up to 10MB</p>
              </>
            )}
          </div>
        </label>
      </div>
    </div>
  );
};

export default AddStudentWizard;
