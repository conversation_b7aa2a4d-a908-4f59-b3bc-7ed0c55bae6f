// src/lib/auth.ts
'use client';

export interface User {
  name: string;
  email: string;
  role: string;
  isAuthenticated: boolean;
}

export const getAuthState = (): User => {
  if (typeof window === 'undefined') {
    return { name: '', email: '', role: '', isAuthenticated: false };
  }

  const isAuthenticated = localStorage.getItem('isAuthenticated') === 'true';
  const userName = localStorage.getItem('userName') || '';
  const userEmail = localStorage.getItem('userEmail') || '';
  const userRole = localStorage.getItem('userRole') || '';

  return {
    name: userName,
    email: userEmail,
    role: userRole,
    isAuthenticated,
  };
};

export const logout = () => {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('isAuthenticated');
    localStorage.removeItem('userName');
    localStorage.removeItem('userEmail');
    localStorage.removeItem('userRole');
    window.location.href = '/product';
  }
};

export const requireAuth = () => {
  if (typeof window !== 'undefined') {
    const { isAuthenticated } = getAuthState();
    if (!isAuthenticated) {
      window.location.href = '/product';
      return false;
    }
    return true;
  }
  return false;
};
