// src/components/dashboard/header.tsx
'use client';

import { useEffect, useRef, useState } from 'react';
import { useAuth } from '../auth/auth-provider';

interface HeaderProps {
  title?: string;
  onNavigate?: (section: string) => void;
}

// Intelligent page name mapping
const getPageDisplayName = (title: string = 'Dashboard'): string => {
  const pageMap: Record<string, string> = {
    'Dashboard': 'Dashboard Overview',
    'Student Management': 'Student Records',
    'Current Students': 'Student Directory',
    'Enroll Student': 'Student Enrollment',
    'Class Assignment': 'Class Management',
    'School Records': 'Academic Records',
    'Staff Management': 'Staff Directory',
    'Attendance Management': 'Attendance Tracking',
    'Leave Management': 'Leave Requests',
    'Academic Management': 'Academic System',
    'Fee Management': 'Fee Collection',
    'Library Management': 'Library System',
    'Transport Management': 'Transport Services',
    'Reports': 'Analytics & Reports',
    'Profile': 'User Profile'
  };
  
  return pageMap[title] || title;
};

const Header = ({ title = 'Dashboard', onNavigate }: HeaderProps) => {
  const { user, signOut } = useAuth();
  const [showDropdown, setShowDropdown] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowDropdown(false);
      }
    };

    if (showDropdown) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [showDropdown]);

  const handleLogout = async () => {
    await signOut();
    setShowDropdown(false);
  };

  const handleNavigation = (section: string) => {
    if (onNavigate) {
      onNavigate(section);
      setShowDropdown(false);
      // Close any open sub-navigation items by triggering a navigation event
      setTimeout(() => {
        const event = new CustomEvent('navigationChange', { detail: { section } });
        window.dispatchEvent(event);
      }, 100);
    }
  };

  const userName = user?.name || 'User';
  const userRole = user?.role || 'student';
  const displayName = getPageDisplayName(title);

  return (
    <header className="bg-gradient-to-r from-slate-900 to-slate-800 border-b border-slate-600 shadow-lg h-14 flex items-center px-4 relative z-30">
      <div className="flex items-center justify-between w-full">
        {/* Intelligent Page Title Display */}
        <div className="flex-1 text-center">
          <h2 className="text-base-app font-semibold text-white tracking-wide">
            {displayName}
          </h2>
        </div>

        {/* Right Side - User Profile Only */}
        <div className="flex items-center">
          {/* User Profile Dropdown */}
          <div className="relative" ref={dropdownRef}>
            <button
              onClick={() => setShowDropdown(!showDropdown)}
              className="flex items-center space-x-2 p-2 rounded-lg hover:bg-slate-800/70 transition-all duration-200 border border-transparent hover:border-slate-600"
            >
              <div className="w-7 h-7 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center shadow-md ring-2 ring-slate-700 hover:ring-slate-600 transition-all duration-200">
                <span className="text-white text-sm-app font-semibold">
                  {userName.charAt(0).toUpperCase()}
                </span>
              </div>
              <div className="text-left hidden sm:block">
                <div className="text-base-app font-medium text-white">{userName}</div>
                <div className="text-sm-app text-gray-300 capitalize opacity-75">{userRole}</div>
              </div>
              <svg 
                className={`w-4 h-4 text-gray-400 transition-transform duration-200 ${showDropdown ? 'rotate-180' : ''}`} 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
              </svg>
            </button>

            {/* Enhanced Dropdown Menu */}
            {showDropdown && (
              <div className="absolute right-0 mt-3 w-56 bg-white rounded-xl shadow-xl border border-gray-200 py-2 z-50 animate-slideIn">
                {/* User Info Header */}
                <div className="px-4 py-3 border-b border-gray-100">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center shadow-md">
                      <span className="text-white text-base-app font-semibold">
                        {userName.charAt(0).toUpperCase()}
                      </span>
                    </div>
                    <div>
                      <div className="text-base-app font-semibold text-gray-900">{userName}</div>
                      <div className="text-sm-app text-gray-500 capitalize">{userRole}</div>
                    </div>
                  </div>
                </div>

                {/* Menu Items */}
                <div className="py-1">
                  <button
                    onClick={() => handleNavigation('Profile')}
                    className="w-full text-left px-4 py-2.5 text-base-app text-gray-700 hover:bg-indigo-50 hover:text-indigo-700 transition-all duration-200 flex items-center space-x-3"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                    <span>Profile Settings</span>
                  </button>
                  
                  <button
                    onClick={() => handleNavigation('Settings')}
                    className="w-full text-left px-4 py-2.5 text-base-app text-gray-700 hover:bg-indigo-50 hover:text-indigo-700 transition-all duration-200 flex items-center space-x-3"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    <span>Account Settings</span>
                  </button>

                  <button
                    onClick={() => handleNavigation('Reports')}
                    className="w-full text-left px-4 py-2.5 text-base-app text-gray-700 hover:bg-indigo-50 hover:text-indigo-700 transition-all duration-200 flex items-center space-x-3"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <span>My Reports</span>
                  </button>
                </div>

                {/* Separator */}
                <hr className="my-2 border-gray-200" />

                {/* Logout Button */}
                <div className="py-1">
                  <button
                    onClick={handleLogout}
                    className="w-full text-left px-4 py-2.5 text-base-app text-red-600 hover:bg-red-50 hover:text-red-700 transition-all duration-200 flex items-center space-x-3"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                    </svg>
                    <span>Logout</span>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
