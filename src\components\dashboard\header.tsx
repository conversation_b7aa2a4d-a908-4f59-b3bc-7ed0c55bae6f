// src/components/dashboard/header.tsx
'use client';

import { useEffect, useState } from 'react';
import { getAuthState, logout } from '../../lib/auth';

interface HeaderProps {
  title?: string;
}

const Header = ({ title: _title = 'Dashboard' }: HeaderProps) => {
  const [user, setUser] = useState({ name: '', role: '' });
  const [showDropdown, setShowDropdown] = useState(false);

  useEffect(() => {
    const authState = getAuthState();
    setUser({ name: authState.name, role: authState.role });
  }, []);

  const handleLogout = () => {
    logout();
  };

  return (
    <header className="bg-slate-800 border-b-2 border-gray-700 px-6 py-3 shadow-sm">
      <div className="flex items-center justify-between h-12">
        {/* Welcome Message */}
        <div>
          <h1 className="text-lg font-bold text-white">
            Welcome back, <span className="text-indigo-400">{user.name}!</span>
          </h1>
          <p className="text-xs text-gray-300 mt-0.5">
            Let's make today productive and inspiring.
          </p>
        </div>

        {/* Right Side - Search, Help, Logout, Profile */}
        <div className="flex items-center space-x-3">
          {/* Search */}
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <input
              type="text"
              placeholder="Search..."
              className="pl-10 pr-4 py-2 bg-slate-700 border border-slate-600 rounded-lg text-sm text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent w-64 hover:bg-slate-600 transition-colors"
            />
          </div>

          {/* Help */}
          <button className="p-2 text-gray-400 hover:text-white transition-colors rounded-lg hover:bg-slate-700">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </button>

          {/* Notifications */}
          <button className="p-2 text-gray-400 hover:text-white transition-colors relative rounded-lg hover:bg-slate-700">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 17h5l-5 5v-5zM10.07 2.82l3.12 3.12M7.05 5.84l3.12 3.12M4.03 8.86l3.12 3.12M1.01 11.88l3.12 3.12" />
            </svg>
            <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center shadow-sm">
              3
            </span>
          </button>

          {/* User Profile Dropdown */}
          <div className="relative">
            <button
              onClick={() => setShowDropdown(!showDropdown)}
              className="flex items-center space-x-2 p-2 rounded-lg hover:bg-slate-700 transition-colors"
            >
              <div className="w-7 h-7 bg-indigo-600 rounded-full flex items-center justify-center shadow-sm">
                <span className="text-white text-xs font-semibold">
                  {user.name.charAt(0)}
                </span>
              </div>
              <div className="text-left hidden sm:block">
                <div className="text-sm font-medium text-white">{user.name}</div>
                <div className="text-xs text-gray-300 capitalize">{user.role}</div>
              </div>
              <svg className="w-3 h-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
              </svg>
            </button>

            {/* Dropdown Menu */}
            {showDropdown && (
              <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50">
                <button className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors">
                  Profile Settings
                </button>
                <button className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors">
                  Account Settings
                </button>
                <hr className="my-1 border-gray-200" />
                <button
                  onClick={handleLogout}
                  className="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors"
                >
                  Logout
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
