// src/components/dashboard/dashboard-content.tsx
'use client';

const DashboardContent = () => {
  return (
    <div className="p-4 bg-gray-100 min-h-full border-l border-gray-300">
      {/* Exciting News Banner - Compact */}
      <div className="bg-gradient-to-r from-indigo-600 to-blue-600 rounded-lg p-4 mb-6 text-white shadow-md">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="bg-white/20 rounded-lg p-2">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div>
              <h3 className="text-base font-semibold">Version 2.0 is Live!</h3>
              <p className="text-blue-100 text-xs">
                Enhanced features, improved performance, and refreshed UI.
              </p>
            </div>
          </div>
          <button className="bg-white text-indigo-600 px-4 py-1.5 rounded-lg text-sm font-semibold hover:bg-blue-50 transition-colors">
            Explore
          </button>
        </div>
      </div>

      {/* Quick Action Cards - Pastel colors */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {/* Create Project */}
        <div className="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg p-4 shadow-sm border border-blue-200/50 hover:shadow-md transition-all duration-300 hover:from-blue-100 hover:to-indigo-150">
          <div className="w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center mb-3 backdrop-blur-sm">
            <svg className="w-5 h-5 text-blue-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
          </div>
          <h3 className="font-semibold text-blue-900 mb-1 text-sm">Create Project</h3>
          <p className="text-xs text-blue-700/80 mb-3">Start a new initiative</p>
          <button className="text-blue-700 text-xs font-medium hover:text-blue-800 hover:underline transition-all">
            Get Started →
          </button>
        </div>

        {/* Upload Docs */}
        <div className="bg-gradient-to-br from-green-50 to-emerald-100 rounded-lg p-4 shadow-sm border border-green-200/50 hover:shadow-md transition-all duration-300 hover:from-green-100 hover:to-emerald-150">
          <div className="w-10 h-10 bg-green-500/20 rounded-lg flex items-center justify-center mb-3 backdrop-blur-sm">
            <svg className="w-5 h-5 text-green-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
            </svg>
          </div>
          <h3 className="font-semibold text-green-900 mb-1 text-sm">Upload Docs</h3>
          <p className="text-xs text-green-700/80 mb-3">Securely store files</p>
          <button className="text-green-700 text-xs font-medium hover:text-green-800 hover:underline transition-all">
            Upload Now →
          </button>
        </div>

        {/* View Analytics */}
        <div className="bg-gradient-to-br from-orange-50 to-amber-100 rounded-lg p-4 shadow-sm border border-orange-200/50 hover:shadow-md transition-all duration-300 hover:from-orange-100 hover:to-amber-150">
          <div className="w-10 h-10 bg-orange-500/20 rounded-lg flex items-center justify-center mb-3 backdrop-blur-sm">
            <svg className="w-5 h-5 text-orange-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          </div>
          <h3 className="font-semibold text-orange-900 mb-1 text-sm">View Analytics</h3>
          <p className="text-xs text-orange-700/80 mb-3">Track progress</p>
          <button className="text-orange-700 text-xs font-medium hover:text-orange-800 hover:underline transition-all">
            View Reports →
          </button>
        </div>

        {/* Manage Users */}
        <div className="bg-gradient-to-br from-pink-50 to-rose-100 rounded-lg p-4 shadow-sm border border-pink-200/50 hover:shadow-md transition-all duration-300 hover:from-pink-100 hover:to-rose-150">
          <div className="w-10 h-10 bg-pink-500/20 rounded-lg flex items-center justify-center mb-3 backdrop-blur-sm">
            <svg className="w-5 h-5 text-pink-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
            </svg>
          </div>
          <h3 className="font-semibold text-pink-900 mb-1 text-sm">Manage Users</h3>
          <p className="text-xs text-pink-700/80 mb-3">Update team access</p>
          <button className="text-pink-700 text-xs font-medium hover:text-pink-800 hover:underline transition-all">
            Manage →
          </button>
        </div>
      </div>

      {/* Search and Filter Bar - Compact */}
      <div className="bg-white rounded-lg p-3 mb-6 shadow-sm border border-gray-300">
        <div className="flex flex-col sm:flex-row gap-3 items-center">
          <div className="flex-1 relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <input
              type="text"
              placeholder="Search items, users, or reports"
              className="pl-9 pr-3 py-2 w-full border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-sm"
            />
          </div>
          <select className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-sm">
            <option>All Categories</option>
            <option>Students</option>
            <option>Staff</option>
            <option>Reports</option>
          </select>
          <input
            type="date"
            className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-sm"
          />
          <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm">
            Filter
          </button>
        </div>
      </div>

      {/* Performance Overview - Compact */}
      <div className="mb-6">
        <h2 className="text-lg font-bold text-gray-900 mb-4">Performance Overview</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* User Engagement */}
          <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-200">
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-semibold text-gray-900 text-sm">User Engagement</h3>
              <div className="text-blue-600">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
              </div>
            </div>
            <div className="text-2xl font-bold text-gray-900 mb-1">+15%</div>
            <div className="text-xs text-gray-600">Last 30 Days</div>
          </div>

          {/* Product Sales */}
          <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-200">
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-semibold text-gray-900 text-sm">Product Sales</h3>
              <div className="text-green-600">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                </svg>
              </div>
            </div>
            <div className="text-2xl font-bold text-gray-900 mb-1">+8%</div>
            <div className="text-xs text-gray-600">Last Quarter</div>
          </div>

          {/* Server Uptime */}
          <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-200">
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-semibold text-gray-900 text-sm">Server Uptime</h3>
              <div className="text-purple-600">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2" />
                </svg>
              </div>
            </div>
            <div className="text-2xl font-bold text-gray-900 mb-1">99.98%</div>
            <div className="text-xs text-gray-600">Last 7 Days</div>
          </div>
        </div>
      </div>

      {/* Recent Activity - Compact */}
      <div>
        <h2 className="text-lg font-bold text-gray-900 mb-4">Recent Activity</h2>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-6">
            <div className="text-center text-gray-500 py-8">
              <svg className="w-10 h-10 mx-auto mb-3 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
              </svg>
              <p className="text-base font-medium">No recent activity</p>
              <p className="text-xs">Activity will appear here as you use the system</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardContent;
