@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Global Typography - Professional Sans Font System */
@layer base {
  * {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  }
    html {
    scroll-behavior: smooth;
    font-size: 0.8rem; /* Reduced base font size for more compact design */
  }
  body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    font-weight: 400;
    line-height: 1.4; /* Tighter line height for compactness */
    color: #1e293b; /* slate-800 */
    background-color: #f8fafc; /* slate-50 */
    font-size: 1rem; /* Default to text-base equivalent (16px) */
  }
}

/* Professional Typography Scale - Based on sidebar font sizes (text-base = 1rem) */
@layer components {  /* Base text classes using sidebar font scale (text-base = 1rem) */
  .text-base-app {
    font-size: 1rem; /* 16px - matches sidebar text-base */
    line-height: 1.5rem; /* 24px */
    font-weight: 400;
  }
  .text-sm-app {
    font-size: 0.875rem; /* 14px - smaller than base */
    line-height: 1.25rem;
    font-weight: 400;
  }
  
  .text-lg-app {
    font-size: 1.125rem; /* 18px - larger than base */
    line-height: 1.75rem;
    font-weight: 500;
  }
  
  .text-xl-app {
    font-size: 1.25rem; /* 20px - heading size */
    line-height: 1.875rem;
    font-weight: 600;
  }
  
  .text-2xl-app {
    font-size: 1.26rem; /* 60% larger than base */
    line-height: 1.89rem;
    font-weight: 700;
  }
  
  /* Form components */
  .form-input {
    @apply text-base-app border border-slate-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent;
    font-family: inherit;
  }
  
  .form-label {
    @apply text-sm-app font-medium text-slate-700 mb-1 block;
  }
  
  .form-button {
    @apply text-base-app font-medium px-4 py-2 rounded-lg transition-colors duration-200;
    font-family: inherit;
  }
  
  .form-button-primary {
    @apply form-button bg-indigo-600 text-white hover:bg-indigo-700;
  }
  
  .form-button-secondary {
    @apply form-button bg-slate-200 text-slate-700 hover:bg-slate-300;
  }
  
  /* Card components */
  .card {
    @apply bg-white rounded-lg shadow-sm border border-slate-200 p-4;
  }
  
  .card-title {
    @apply text-lg-app font-semibold text-slate-900 mb-2;
  }
  
  .card-content {
    @apply text-base-app text-slate-700;
  }
  
  .card-subtitle {
    @apply text-sm-app text-slate-600;
  }
  
  /* Table components */
  .table-header {
    @apply text-sm-app font-semibold text-slate-900 uppercase tracking-wide;
  }
  
  .table-cell {
    @apply text-base-app text-slate-700;
  }
  
  /* Button variants */
  .btn {
    @apply text-base-app font-medium px-4 py-2 rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
    font-family: inherit;
  }
  
  .btn-primary {
    @apply btn bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-indigo-500;
  }
  
  .btn-secondary {
    @apply btn bg-slate-200 text-slate-700 hover:bg-slate-300 focus:ring-slate-500;
  }
  
  .btn-danger {
    @apply btn bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
  }
  
  .btn-success {
    @apply btn bg-emerald-600 text-white hover:bg-emerald-700 focus:ring-emerald-500;
  }
  
  /* Modal and overlay components */
  .modal-title {
    @apply text-xl-app font-semibold text-slate-900;
  }
  
  .modal-content {
    @apply text-base-app text-slate-700;
  }
}

/* Custom fade-in animation */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Modern Auth Modal Animations */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Enhanced dropdown slideIn animation */
@keyframes slideInDropdown {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes modalBackdrop {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(12px);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes pulse-border {
  0%, 100% {
    border-color: rgb(99 102 241 / 0.3);
    box-shadow: 0 0 0 0 rgb(99 102 241 / 0.7);
  }
  70% {
    border-color: rgb(99 102 241 / 0.6);
    box-shadow: 0 0 0 3px rgb(99 102 241 / 0);
  }
}

/* Animation classes */
.animate-fadeInUp {
  animation: fadeInUp 0.8s ease-out forwards;
}

.animate-fadeIn {
  animation: fadeIn 0.6s ease-out forwards;
}

.animate-slideIn {
  animation: slideIn 0.3s ease-out forwards;
}

.animate-slideInLeft {
  animation: slideInFromLeft 0.8s ease-out forwards;
}

.animate-slideInRight {
  animation: slideInFromRight 0.8s ease-out forwards;
}

.animate-float {
  animation: floatAnimation 3s ease-in-out infinite;
}

.animate-pulseGlow {
  animation: pulseGlow 2s ease-in-out infinite;
}

.animate-modalBackdrop {
  animation: modalBackdrop 0.3s ease-out;
}

.animate-shimmer {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  background-size: 200px 100%;
  animation: shimmer 2s infinite;
}

.animate-pulse-border {
  animation: pulse-border 2s infinite;
}

/* Hover effects */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* Gradient text effect */
.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Glass morphism effect */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Custom button styles */
.btn-primary {
  @apply bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-semibold py-3 px-6 rounded-lg shadow-lg transition-all duration-200 hover:from-indigo-700 hover:to-purple-700 hover:shadow-xl transform hover:-translate-y-1;
}

.btn-secondary {
  @apply border-2 border-indigo-600 text-indigo-600 font-semibold py-3 px-6 rounded-lg transition-all duration-200 hover:bg-indigo-600 hover:text-white transform hover:-translate-y-1;
}

/* Loading state for form submissions */
.loading-spinner {
  border: 2px solid #f3f3f3;
  border-top: 2px solid #3498db;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Enhanced focus states for accessibility */
.focus-visible:focus {
  outline: 2px solid #6366f1;
  outline-offset: 2px;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Dark theme scrollbar */
.dark ::-webkit-scrollbar-track {
  background: #1e293b;
}

.dark ::-webkit-scrollbar-thumb {
  background: #475569;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* Grid pattern for hero section */
.bg-grid-pattern {
  background-image: radial-gradient(circle, #e2e8f0 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Enhanced form field styles */
.form-field-enhanced {
  @apply relative;
}

.form-field-enhanced input:focus + .field-icon {
  @apply text-indigo-500 scale-110;
}

.form-field-enhanced:hover .field-icon {
  @apply text-slate-500;
}

/* Loading spinner enhancement */
@keyframes spin-enhanced {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.loading-spinner-enhanced {
  animation: spin-enhanced 1s linear infinite;
}

/* Enhanced sidebar and navigation styles */

/* Sidebar navigation hover effects */
.nav-item-enhanced {
  @apply relative overflow-hidden;
}

.nav-item-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.1), transparent);
  transition: left 0.6s ease;
}

.nav-item-enhanced:hover::before {
  left: 100%;
}

/* Active nav item glow effect */
.nav-item-active {
  box-shadow: 
    0 0 20px rgba(99, 102, 241, 0.3),
    0 0 40px rgba(99, 102, 241, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Smooth sidebar transitions */
.sidebar-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced glass morphism effect */
.glass-morphism-enhanced {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Professional shadow utilities */
.shadow-professional {
  box-shadow: 
    0 1px 3px rgba(0, 0, 0, 0.12),
    0 1px 2px rgba(0, 0, 0, 0.24);
}

.shadow-professional-lg {
  box-shadow: 
    0 10px 25px rgba(0, 0, 0, 0.1),
    0 6px 10px rgba(0, 0, 0, 0.12);
}

/* System status indicator animation */
@keyframes pulse-gentle {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

.pulse-gentle {
  animation: pulse-gentle 2s infinite ease-in-out;
}

/* Enhanced gradient text */
.gradient-text-professional {
  background: linear-gradient(135deg, #1e293b 0%, #475569 50%, #334155 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Custom sidebar font sizes - 5% smaller */
.text-xs-small {
  font-size: 0.7125rem; /* 0.75rem * 0.95 = 0.7125rem */
  line-height: 1.1875rem; /* 1.25rem * 0.95 = 1.1875rem */
}

.text-sm-small {
  font-size: 0.83125rem; /* 0.875rem * 0.95 = 0.83125rem */
  line-height: 1.30625rem; /* 1.375rem * 0.95 = 1.30625rem */
}

/* Custom sidebar font sizes - 5% larger than original */
.text-xs-large {
  font-size: 0.7875rem; /* 0.75rem * 1.05 = 0.7875rem */
  line-height: 1.3125rem; /* 1.25rem * 1.05 = 1.3125rem */
}

.text-sm-large {
  font-size: 0.91875rem; /* 0.875rem * 1.05 = 0.91875rem */
  line-height: 1.44375rem; /* 1.375rem * 1.05 = 1.44375rem */
}

/* Custom sidebar widths - 3% wider */
.w-14-plus {
  width: 3.625rem; /* 56px * 1.03 = 57.68px ≈ 58px = 3.625rem */
}

.w-62 {
  width: 15.5rem; /* 240px * 1.03 = 247.2px ≈ 248px = 15.5rem */
}

/* Custom margins to match sidebar widths */
.ml-14-plus {
  margin-left: 3.625rem; /* Match w-14-plus */
}

.ml-62 {
  margin-left: 15.5rem; /* Match w-62 */
}

/* Custom left positioning for separator */
.left-14-plus {
  left: 3.625rem; /* Match w-14-plus */
}

.left-62 {
  left: 15.5rem; /* Match w-62 */
}

/* Smooth Animation for Tooltips */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-10px) translateY(-50%);
  }
  to {
    opacity: 1;
    transform: translateX(0) translateY(-50%);
  }
}
