// src/app/dashboard/layout.tsx
'use client';

import { useEffect, useState } from 'react';
import PageWrapper from '../../components/common/page-wrapper';
import DashboardContent from '../../components/dashboard/dashboard-content';
import Header from '../../components/dashboard/header';
import Sidebar from '../../components/dashboard/sidebar';
import ProfilePage from '../../components/profile/profile-page';
import StudentManagement from '../../components/student-management/student-management';
import { requireAuth } from '../../lib/auth';

const StaffManagement = () => (
  <PageWrapper>
    <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-300">
      <p className="text-gray-600 text-sm">Staff management features will be implemented here.</p>
    </div>
  </PageWrapper>
);

const AttendanceManagement = () => (
  <PageWrapper>
    <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-300">
      <p className="text-gray-600 text-sm">Attendance management features will be implemented here.</p>
    </div>
  </PageWrapper>
);

const LeaveManagement = () => (
  <PageWrapper>
    <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-300">
      <p className="text-gray-600 text-sm">Leave management features will be implemented here.</p>
    </div>
  </PageWrapper>
);

const AcademicManagement = () => (
  <PageWrapper>
    <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-300">
      <p className="text-gray-600 text-sm">Academic management features will be implemented here.</p>
    </div>
  </PageWrapper>
);

const FeeManagement = () => (
  <PageWrapper>
    <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-300">
      <p className="text-gray-600 text-sm">Fee management features will be implemented here.</p>
    </div>
  </PageWrapper>
);

const LibraryManagement = () => (
  <PageWrapper>
    <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-300">
      <p className="text-gray-600 text-sm">Library management features will be implemented here.</p>
    </div>
  </PageWrapper>
);

const TransportManagement = () => (
  <PageWrapper>
    <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-300">
      <p className="text-gray-600 text-sm">Transport management features will be implemented here.</p>
    </div>
  </PageWrapper>
);

const Reports = () => (
  <PageWrapper>
    <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-300">
      <p className="text-gray-600 text-sm">Reports and analytics will be implemented here.</p>
    </div>
  </PageWrapper>
);

export default function DashboardLayout({
  children: _children,
}: {
  children: React.ReactNode;
}) {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [activeSection, setActiveSection] = useState('Dashboard');
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);

  // Pages that should open in full-page mode by default (sidebar hidden)
  const fullPageSections = ['Profile'];

  useEffect(() => {
    const checkAuth = async () => {
      const authResult = await requireAuth();
      setIsAuthenticated(authResult);
      setIsLoading(false);
    };

    checkAuth();
  }, []);

  const handleSectionChange = (section: string) => {
    setActiveSection(section);
    // If navigating to a full-page section, ensure sidebar is hidden
    if (fullPageSections.includes(section)) {
      setIsSidebarCollapsed(true);
    }
  };

  const toggleSidebar = () => {
    setIsSidebarCollapsed(!isSidebarCollapsed);
  };

  const renderContent = () => {
    switch (activeSection) {
      case 'Dashboard':
        return <DashboardContent />;
      case 'Student Management':
      case 'Current Students':
      case 'Enroll Student':
      case 'Class Assignment':
      case 'School Records':
        return <StudentManagement activeSubSection={activeSection} />;
      case 'Staff Management':
        return <StaffManagement />;
      case 'Attendance Management':
        return <AttendanceManagement />;
      case 'Leave Management':
        return <LeaveManagement />;
      case 'Academic Management':
        return <AcademicManagement />;
      case 'Fee Management':
        return <FeeManagement />;
      case 'Library Management':
        return <LibraryManagement />;
      case 'Transport Management':
        return <TransportManagement />;
      case 'Reports':
        return <Reports />;
      case 'Profile':
        return <ProfilePage />;
      default:
        return <DashboardContent />;
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null; // requireAuth will handle the redirect
  }

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Sidebar - Fixed position */}
      <div className="flex-shrink-0 transition-all duration-300 relative">
        <Sidebar 
          activeItem={activeSection} 
          onItemClick={handleSectionChange}
          isCollapsed={isSidebarCollapsed}
          onToggleCollapse={toggleSidebar}
        />
      </div>

      {/* Main Content Area with proper margin for fixed sidebar */}
      <div className={`flex flex-col min-h-screen transition-all duration-300 ${
        isSidebarCollapsed ? 'ml-14-plus' : 'ml-62'
      }`}>
        {/* Elegant separation line */}
        <div className={`absolute top-0 bottom-0 w-0.5 bg-gradient-to-b from-gray-200 via-gray-300 to-gray-200 z-10 transition-all duration-300 ${
          isSidebarCollapsed ? 'left-14-plus' : 'left-62'
        }`}></div>
        
        {/* Header - Fixed at top */}
        <div className="flex-shrink-0">
          <Header 
            title={activeSection} 
            onNavigate={handleSectionChange}
          />
        </div>

        {/* Page Content - Scrollable */}
        <main className="flex-1 overflow-y-auto bg-gray-100 pl-2">
          {renderContent()}
        </main>
      </div>
    </div>
  );
}
