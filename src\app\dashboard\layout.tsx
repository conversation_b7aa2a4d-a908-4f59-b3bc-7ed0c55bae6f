// src/app/dashboard/layout.tsx
'use client';

import { useEffect, useState } from 'react';
import DashboardContent from '../../components/dashboard/dashboard-content';
import Header from '../../components/dashboard/header';
import Sidebar from '../../components/dashboard/sidebar';
import StudentManagement from '../../components/student-management/student-management';
import { requireAuth } from '../../lib/auth';

const StaffManagement = () => (
  <div className="p-4 bg-gray-100 min-h-full">
    <h1 className="text-lg font-bold text-gray-900 mb-4">Staff Management</h1>
    <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-300">
      <p className="text-gray-600 text-sm">Staff management features will be implemented here.</p>
    </div>
  </div>
);

const AttendanceManagement = () => (
  <div className="p-4 bg-gray-100 min-h-full">
    <h1 className="text-lg font-bold text-gray-900 mb-4">Attendance Management</h1>
    <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-300">
      <p className="text-gray-600 text-sm">Attendance management features will be implemented here.</p>
    </div>
  </div>
);

const LeaveManagement = () => (
  <div className="p-4 bg-gray-100 min-h-full">
    <h1 className="text-lg font-bold text-gray-900 mb-4">Leave Management</h1>
    <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-300">
      <p className="text-gray-600 text-sm">Leave management features will be implemented here.</p>
    </div>
  </div>
);

const AcademicManagement = () => (
  <div className="p-4 bg-gray-100 min-h-full">
    <h1 className="text-lg font-bold text-gray-900 mb-4">Academic Management</h1>
    <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-300">
      <p className="text-gray-600 text-sm">Academic management features will be implemented here.</p>
    </div>
  </div>
);

const FeeManagement = () => (
  <div className="p-4 bg-gray-100 min-h-full">
    <h1 className="text-lg font-bold text-gray-900 mb-4">Fee Management</h1>
    <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-300">
      <p className="text-gray-600 text-sm">Fee management features will be implemented here.</p>
    </div>
  </div>
);

const LibraryManagement = () => (
  <div className="p-4 bg-gray-100 min-h-full">
    <h1 className="text-lg font-bold text-gray-900 mb-4">Library Management</h1>
    <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-300">
      <p className="text-gray-600 text-sm">Library management features will be implemented here.</p>
    </div>
  </div>
);

const TransportManagement = () => (
  <div className="p-4 bg-gray-100 min-h-full">
    <h1 className="text-lg font-bold text-gray-900 mb-4">Transport Management</h1>
    <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-300">
      <p className="text-gray-600 text-sm">Transport management features will be implemented here.</p>
    </div>
  </div>
);

const Reports = () => (
  <div className="p-4 bg-gray-100 min-h-full">
    <h1 className="text-lg font-bold text-gray-900 mb-4">Reports</h1>
    <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-300">
      <p className="text-gray-600 text-sm">Reports and analytics will be implemented here.</p>
    </div>
  </div>
);

export default function DashboardLayout({
  children: _children,
}: {
  children: React.ReactNode;
}) {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [activeSection, setActiveSection] = useState('Dashboard');

  useEffect(() => {
    const checkAuth = () => {
      const authResult = requireAuth();
      setIsAuthenticated(authResult);
      setIsLoading(false);
    };

    checkAuth();
  }, []);

  const renderContent = () => {
    switch (activeSection) {
      case 'Dashboard':
        return <DashboardContent />;
      case 'Student Management':
      case 'Current Students':
      case 'Enroll Student':
      case 'Class Assignment':
      case 'School Records':
        return <StudentManagement activeSubSection={activeSection} />;
      case 'Staff Management':
        return <StaffManagement />;
      case 'Attendance Management':
        return <AttendanceManagement />;
      case 'Leave Management':
        return <LeaveManagement />;
      case 'Academic Management':
        return <AcademicManagement />;
      case 'Fee Management':
        return <FeeManagement />;
      case 'Library Management':
        return <LibraryManagement />;
      case 'Transport Management':
        return <TransportManagement />;
      case 'Reports':
        return <Reports />;
      default:
        return <DashboardContent />;
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null; // requireAuth will handle the redirect
  }

  return (
    <div className="min-h-screen bg-gray-100 flex overflow-hidden">
      {/* Sidebar - Fixed height to viewport */}
      <div className="flex-shrink-0">
        <Sidebar activeItem={activeSection} onItemClick={setActiveSection} />
      </div>
      
      {/* Main Content Area with clear separation */}
      <div className="flex-1 flex flex-col min-h-screen border-l-2 border-gray-300">
        {/* Header */}
        <Header />
        
        {/* Page Content */}
        <main className="flex-1 overflow-y-auto bg-gray-100">
          {renderContent()}
        </main>
      </div>
    </div>
  );
}
