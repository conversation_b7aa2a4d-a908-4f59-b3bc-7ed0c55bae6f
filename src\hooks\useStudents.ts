// src/hooks/useStudents.ts
import { useEffect, useState } from 'react';
import { CreateStudentData, StudentService, StudentWithRelations } from '../services/studentService';

interface UseStudentsReturn {
  students: StudentWithRelations[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  createStudent: (data: CreateStudentData) => Promise<void>;
  updateStudent: (id: string, data: Partial<CreateStudentData>) => Promise<void>;
  deleteStudent: (id: string) => Promise<void>;
  searchStudents: (query: string) => Promise<StudentWithRelations[]>;
}

export const useStudents = (): UseStudentsReturn => {
  const [students, setStudents] = useState<StudentWithRelations[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchStudents = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await StudentService.getAllStudents();
      setStudents(data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch students';
      setError(errorMessage);
      console.error('Error fetching students:', err);
    } finally {
      setLoading(false);
    }
  };

  const createStudent = async (data: CreateStudentData) => {
    try {
      setError(null);
      await StudentService.createStudent(data);
      await fetchStudents(); // Refetch to update the list
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create student';
      setError(errorMessage);
      throw err;
    }
  };

  const updateStudent = async (id: string, data: Partial<CreateStudentData>) => {
    try {
      setError(null);
      await StudentService.updateStudent(id, data);
      await fetchStudents(); // Refetch to update the list
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update student';
      setError(errorMessage);
      throw err;
    }
  };

  const deleteStudent = async (id: string) => {
    try {
      setError(null);
      await StudentService.deleteStudent(id);
      await fetchStudents(); // Refetch to update the list
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete student';
      setError(errorMessage);
      throw err;
    }
  };

  const searchStudents = async (query: string): Promise<StudentWithRelations[]> => {
    try {
      setError(null);
      return await StudentService.searchStudents(query);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to search students';
      setError(errorMessage);
      throw err;
    }
  };

  useEffect(() => {
    fetchStudents();
  }, []);

  return {
    students,
    loading,
    error,
    refetch: fetchStudents,
    createStudent,
    updateStudent,
    deleteStudent,
    searchStudents
  };
};

// Hook for getting a single student
export const useStudent = (id: string | null) => {
  const [student, setStudent] = useState<StudentWithRelations | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!id) {
      setStudent(null);
      setLoading(false);
      return;
    }

    const fetchStudent = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await StudentService.getStudentById(id);
        setStudent(data);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to fetch student';
        setError(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    fetchStudent();
  }, [id]);

  return { student, loading, error };
};
