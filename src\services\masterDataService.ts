// src/services/masterDataService.ts
import { DATABASE_TABLES } from '../config/database';
import { supabase } from '../lib/supabase';
import type { AcademicYear, Class, GuardianRelation, Section } from '../types/database';

export class MasterDataService {  /**
   * Get all active classes
   */
  static async getClasses(): Promise<Class[]> {
    const { data, error } = await supabase
      .from(DATABASE_TABLES.CLASSES)
      .select('*')
      .eq('is_active', true)
      .order('name');

    if (error) {
      console.error('Error fetching classes:', error);
      throw new Error(`Failed to fetch classes: ${error.message}`);
    }

    return data || [];
  }
  /**
   * Get all active sections
   */
  static async getSections(): Promise<Section[]> {
    const { data, error } = await supabase
      .from(DATABASE_TABLES.SECTIONS)
      .select('*')
      .eq('is_active', true)
      .order('name');

    if (error) {
      console.error('Error fetching sections:', error);
      throw new Error(`Failed to fetch sections: ${error.message}`);
    }

    return data || [];
  }
  /**
   * Get all active academic years
   */
  static async getAcademicYears(): Promise<AcademicYear[]> {
    const { data, error } = await supabase
      .from(DATABASE_TABLES.ACADEMIC_YEARS)
      .select('*')
      .eq('is_active', true)
      .order('start_date', { ascending: false });

    if (error) {
      console.error('Error fetching academic years:', error);
      throw new Error(`Failed to fetch academic years: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Get current academic year
   */  static async getCurrentAcademicYear(): Promise<AcademicYear | null> {
    const { data, error } = await supabase
      .from(DATABASE_TABLES.ACADEMIC_YEARS)
      .select('*')
      .eq('is_current', true)
      .eq('is_active', true)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        // No current academic year found
        return null;
      }
      console.error('Error fetching current academic year:', error);
      throw new Error(`Failed to fetch current academic year: ${error.message}`);
    }

    return data;
  }
  /**
   * Get all active guardian relations
   */
  static async getGuardianRelations(): Promise<GuardianRelation[]> {
    const { data, error } = await supabase
      .from(DATABASE_TABLES.GUARDIAN_RELATIONS)
      .select('*')
      .eq('is_active', true)
      .order('name');

    if (error) {
      console.error('Error fetching guardian relations:', error);
      throw new Error(`Failed to fetch guardian relations: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Get all master data in one call for forms
   */
  static async getAllMasterData() {
    try {
      const [classes, sections, academicYears, guardianRelations, currentAcademicYear] = await Promise.all([
        this.getClasses(),
        this.getSections(),
        this.getAcademicYears(),
        this.getGuardianRelations(),
        this.getCurrentAcademicYear()
      ]);

      return {
        classes,
        sections,
        academicYears,
        guardianRelations,
        currentAcademicYear
      };
    } catch (error) {
      console.error('Error fetching all master data:', error);
      throw error;
    }
  }

  /**
   * Check if roll number exists in a class and section for an academic year
   */
  static async isRollNumberExists(
    rollNumber: string, 
    classId: string, 
    sectionId: string, 
    academicYearId: string,
    excludeStudentId?: string
  ): Promise<boolean> {    let query = supabase
      .from(DATABASE_TABLES.STUDENTS)
      .select('id')
      .eq('roll_number', rollNumber)
      .eq('class_id', classId)
      .eq('section_id', sectionId)
      .eq('academic_year_id', academicYearId)
      .eq('is_active', true);

    if (excludeStudentId) {
      query = query.neq('id', excludeStudentId);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error checking roll number:', error);
      throw new Error(`Failed to check roll number: ${error.message}`);
    }

    return (data || []).length > 0;
  }
}
