// src/services/enrollment/enrollmentService.ts
import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '../../types/database';
import { BaseService } from '../core/baseService';
import { <PERSON>Error, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '../core/errorHandler';
import { EnhancedStudentService, StudentEnrollmentData } from '../student/enhancedStudentService';
import { ValidationService } from './validationService';

/**
 * Enrollment step status
 */
export enum EnrollmentStep {
  PERSONAL_INFO = 'personal_info',
  GUARDIAN_DETAILS = 'guardian_details',
  ACADEMIC_INFO = 'academic_info',
  DOCUMENTS = 'documents',
  COMPLETED = 'completed'
}

/**
 * Enrollment status
 */
export enum EnrollmentStatus {
  DRAFT = 'draft',
  IN_PROGRESS = 'in_progress',
  PENDING_REVIEW = 'pending_review',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  COMPLETED = 'completed'
}

/**
 * Enrollment session data
 */
export interface EnrollmentSession {
  id: string;
  currentStep: EnrollmentStep;
  status: EnrollmentStatus;
  data: Partial<StudentEnrollmentData>;
  validationErrors: Record<string, string[]>;
  createdAt: string;
  updatedAt: string;
  expiresAt: string;
}

/**
 * Enrollment result
 */
export interface EnrollmentResult {
  success: boolean;
  studentId?: string;
  enrollmentId?: string;
  errors?: string[];
  warnings?: string[];
  rollbackPerformed?: boolean;
}

/**
 * Enrollment progress
 */
export interface EnrollmentProgress {
  currentStep: EnrollmentStep;
  completedSteps: EnrollmentStep[];
  totalSteps: number;
  percentage: number;
  isComplete: boolean;
  nextStep?: EnrollmentStep;
}

/**
 * Enrollment service for orchestrating the complete student enrollment process
 */
export class EnrollmentService extends BaseService {
  private studentService: EnhancedStudentService;
  private validationService: ValidationService;
  private readonly sessionTimeout = 24 * 60 * 60 * 1000; // 24 hours

  constructor(client: SupabaseClient<Database>) {
    super(client);
    this.studentService = new EnhancedStudentService(client);
    this.validationService = new ValidationService(client);
  }

  /**
   * Start a new enrollment session
   */
  async startEnrollment(): Promise<EnrollmentSession> {
    try {
      this.log('info', 'Starting new enrollment session');

      const session: EnrollmentSession = {
        id: this.generateId(),
        currentStep: EnrollmentStep.PERSONAL_INFO,
        status: EnrollmentStatus.DRAFT,
        data: {},
        validationErrors: {},
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        expiresAt: new Date(Date.now() + this.sessionTimeout).toISOString()
      };

      // Store session in localStorage or database
      await this.saveEnrollmentSession(session);

      this.log('info', 'Enrollment session started', { sessionId: session.id });

      return session;
    } catch (error) {
      throw ErrorHandler.handle(error, 'Start enrollment');
    }
  }

  /**
   * Update enrollment session with step data
   */
  async updateEnrollmentStep(
    sessionId: string,
    step: EnrollmentStep,
    stepData: any
  ): Promise<EnrollmentSession> {
    try {
      this.log('info', 'Updating enrollment step', { sessionId, step });

      // Get current session
      const session = await this.getEnrollmentSession(sessionId);
      if (!session) {
        throw new ServiceError(
          ErrorCode.NOT_FOUND,
          'Enrollment session not found',
          null,
          'Update enrollment step'
        );
      }

      // Check if session is expired
      if (new Date() > new Date(session.expiresAt)) {
        throw new ServiceError(
          ErrorCode.SESSION_EXPIRED,
          'Enrollment session has expired',
          null,
          'Update enrollment step'
        );
      }

      // Validate step data
      const validationResult = await this.validationService.validateStep(step, stepData);
      if (!validationResult.isValid) {
        session.validationErrors[step] = validationResult.errors;
        await this.saveEnrollmentSession(session);
        
        throw new ServiceError(
          ErrorCode.VALIDATION_ERROR,
          'Step validation failed',
          validationResult.errors,
          'Update enrollment step'
        );
      }

      // Update session data
      session.data = {
        ...session.data,
        [this.getDataKeyForStep(step)]: stepData
      };

      session.currentStep = this.getNextStep(step);
      session.status = session.currentStep === EnrollmentStep.COMPLETED ? 
        EnrollmentStatus.PENDING_REVIEW : 
        EnrollmentStatus.IN_PROGRESS;
      session.updatedAt = new Date().toISOString();
      session.validationErrors[step] = []; // Clear validation errors for this step

      // Save updated session
      await this.saveEnrollmentSession(session);

      this.log('info', 'Enrollment step updated', { 
        sessionId, 
        step, 
        nextStep: session.currentStep 
      });

      return session;
    } catch (error) {
      throw ErrorHandler.handle(error, 'Update enrollment step');
    }
  }

  /**
   * Complete the enrollment process
   */
  async completeEnrollment(sessionId: string): Promise<EnrollmentResult> {
    try {
      this.log('info', 'Completing enrollment', { sessionId });

      // Get enrollment session
      const session = await this.getEnrollmentSession(sessionId);
      if (!session) {
        throw new ServiceError(
          ErrorCode.NOT_FOUND,
          'Enrollment session not found',
          null,
          'Complete enrollment'
        );
      }

      // Validate all steps are completed
      const validationResult = await this.validationService.validateCompleteEnrollment(session.data);
      if (!validationResult.isValid) {
        return {
          success: false,
          errors: validationResult.errors,
          enrollmentId: sessionId
        };
      }

      // Convert session data to enrollment data
      const enrollmentData = this.convertSessionToEnrollmentData(session.data);

      // Perform enrollment with retry logic
      const result = await RetryHandler.withRetry(
        async () => this.studentService.enrollStudent(enrollmentData),
        3,
        1000
      );

      // Update session status
      session.status = EnrollmentStatus.COMPLETED;
      session.currentStep = EnrollmentStep.COMPLETED;
      session.updatedAt = new Date().toISOString();
      await this.saveEnrollmentSession(session);

      // Clean up session after successful enrollment
      await this.cleanupEnrollmentSession(sessionId);

      this.log('info', 'Enrollment completed successfully', {
        sessionId,
        studentId: result.student.id
      });

      return {
        success: true,
        studentId: result.student.id,
        enrollmentId: sessionId
      };

    } catch (error) {
      this.log('error', 'Enrollment completion failed', { error, sessionId });

      // Update session with error status
      try {
        const session = await this.getEnrollmentSession(sessionId);
        if (session) {
          session.status = EnrollmentStatus.REJECTED;
          session.updatedAt = new Date().toISOString();
          await this.saveEnrollmentSession(session);
        }
      } catch (updateError) {
        this.log('warn', 'Failed to update session status after error', updateError);
      }

      const serviceError = ErrorHandler.handle(error, 'Complete enrollment');
      
      return {
        success: false,
        errors: [serviceError.userMessage || serviceError.message],
        enrollmentId: sessionId,
        rollbackPerformed: true
      };
    }
  }

  /**
   * Get enrollment progress
   */
  async getEnrollmentProgress(sessionId: string): Promise<EnrollmentProgress> {
    try {
      const session = await this.getEnrollmentSession(sessionId);
      if (!session) {
        throw new ServiceError(
          ErrorCode.NOT_FOUND,
          'Enrollment session not found',
          null,
          'Get enrollment progress'
        );
      }

      const allSteps = [
        EnrollmentStep.PERSONAL_INFO,
        EnrollmentStep.GUARDIAN_DETAILS,
        EnrollmentStep.ACADEMIC_INFO,
        EnrollmentStep.DOCUMENTS
      ];

      const completedSteps = this.getCompletedSteps(session);
      const percentage = Math.round((completedSteps.length / allSteps.length) * 100);

      return {
        currentStep: session.currentStep,
        completedSteps,
        totalSteps: allSteps.length,
        percentage,
        isComplete: session.status === EnrollmentStatus.COMPLETED,
        nextStep: session.currentStep !== EnrollmentStep.COMPLETED ? 
          session.currentStep : undefined
      };
    } catch (error) {
      throw ErrorHandler.handle(error, 'Get enrollment progress');
    }
  }

  /**
   * Resume enrollment session
   */
  async resumeEnrollment(sessionId: string): Promise<EnrollmentSession> {
    try {
      const session = await this.getEnrollmentSession(sessionId);
      if (!session) {
        throw new ServiceError(
          ErrorCode.NOT_FOUND,
          'Enrollment session not found',
          null,
          'Resume enrollment'
        );
      }

      // Check if session is expired
      if (new Date() > new Date(session.expiresAt)) {
        throw new ServiceError(
          ErrorCode.SESSION_EXPIRED,
          'Enrollment session has expired',
          null,
          'Resume enrollment'
        );
      }

      // Extend session expiry
      session.expiresAt = new Date(Date.now() + this.sessionTimeout).toISOString();
      session.updatedAt = new Date().toISOString();
      
      await this.saveEnrollmentSession(session);

      this.log('info', 'Enrollment session resumed', { sessionId });

      return session;
    } catch (error) {
      throw ErrorHandler.handle(error, 'Resume enrollment');
    }
  }

  /**
   * Cancel enrollment session
   */
  async cancelEnrollment(sessionId: string): Promise<void> {
    try {
      await this.cleanupEnrollmentSession(sessionId);
      this.log('info', 'Enrollment session cancelled', { sessionId });
    } catch (error) {
      throw ErrorHandler.handle(error, 'Cancel enrollment');
    }
  }

  // Private helper methods

  private async saveEnrollmentSession(session: EnrollmentSession): Promise<void> {
    try {
      // For now, save to localStorage
      // In production, save to database
      if (typeof window !== 'undefined') {
        localStorage.setItem(`enrollment_session_${session.id}`, JSON.stringify(session));
      }
    } catch (error) {
      throw new ServiceError(
        ErrorCode.UNKNOWN_ERROR,
        'Failed to save enrollment session',
        error,
        'Save enrollment session'
      );
    }
  }

  private async getEnrollmentSession(sessionId: string): Promise<EnrollmentSession | null> {
    try {
      // For now, get from localStorage
      // In production, get from database
      if (typeof window !== 'undefined') {
        const sessionData = localStorage.getItem(`enrollment_session_${sessionId}`);
        return sessionData ? JSON.parse(sessionData) : null;
      }
      return null;
    } catch (error) {
      this.log('warn', 'Failed to get enrollment session', { error, sessionId });
      return null;
    }
  }

  private async cleanupEnrollmentSession(sessionId: string): Promise<void> {
    try {
      if (typeof window !== 'undefined') {
        localStorage.removeItem(`enrollment_session_${sessionId}`);
      }
    } catch (error) {
      this.log('warn', 'Failed to cleanup enrollment session', { error, sessionId });
    }
  }

  private getDataKeyForStep(step: EnrollmentStep): string {
    const keyMap = {
      [EnrollmentStep.PERSONAL_INFO]: 'student',
      [EnrollmentStep.GUARDIAN_DETAILS]: 'guardian',
      [EnrollmentStep.ACADEMIC_INFO]: 'academic',
      [EnrollmentStep.DOCUMENTS]: 'documents'
    };
    return keyMap[step] || step;
  }

  private getNextStep(currentStep: EnrollmentStep): EnrollmentStep {
    const stepOrder = [
      EnrollmentStep.PERSONAL_INFO,
      EnrollmentStep.GUARDIAN_DETAILS,
      EnrollmentStep.ACADEMIC_INFO,
      EnrollmentStep.DOCUMENTS,
      EnrollmentStep.COMPLETED
    ];

    const currentIndex = stepOrder.indexOf(currentStep);
    return stepOrder[currentIndex + 1] || EnrollmentStep.COMPLETED;
  }

  private getCompletedSteps(session: EnrollmentSession): EnrollmentStep[] {
    const completed: EnrollmentStep[] = [];
    
    if (session.data.student) completed.push(EnrollmentStep.PERSONAL_INFO);
    if (session.data.guardian) completed.push(EnrollmentStep.GUARDIAN_DETAILS);
    if (session.data.academic) completed.push(EnrollmentStep.ACADEMIC_INFO);
    if (session.data.documents) completed.push(EnrollmentStep.DOCUMENTS);

    return completed;
  }

  private convertSessionToEnrollmentData(sessionData: Partial<StudentEnrollmentData>): StudentEnrollmentData {
    if (!sessionData.student || !sessionData.guardian || !sessionData.academic) {
      throw new ServiceError(
        ErrorCode.VALIDATION_ERROR,
        'Incomplete enrollment data',
        null,
        'Convert session data'
      );
    }

    return {
      student: sessionData.student,
      guardian: sessionData.guardian,
      academic: sessionData.academic,
      documents: sessionData.documents || []
    };
  }

  /**
   * Get all active enrollment sessions (for admin)
   */
  async getActiveEnrollmentSessions(): Promise<EnrollmentSession[]> {
    try {
      // In production, this would query the database
      // For now, return empty array
      return [];
    } catch (error) {
      throw ErrorHandler.handle(error, 'Get active enrollment sessions');
    }
  }

  /**
   * Clean up expired sessions
   */
  async cleanupExpiredSessions(): Promise<number> {
    try {
      let cleanedCount = 0;
      
      // In production, this would query and clean up expired sessions from database
      // For now, clean up from localStorage
      if (typeof window !== 'undefined') {
        const keys = Object.keys(localStorage);
        const sessionKeys = keys.filter(key => key.startsWith('enrollment_session_'));
        
        for (const key of sessionKeys) {
          try {
            const sessionData = localStorage.getItem(key);
            if (sessionData) {
              const session: EnrollmentSession = JSON.parse(sessionData);
              if (new Date() > new Date(session.expiresAt)) {
                localStorage.removeItem(key);
                cleanedCount++;
              }
            }
          } catch (error) {
            // Remove corrupted session data
            localStorage.removeItem(key);
            cleanedCount++;
          }
        }
      }

      this.log('info', 'Cleaned up expired enrollment sessions', { count: cleanedCount });
      return cleanedCount;
    } catch (error) {
      throw ErrorHandler.handle(error, 'Cleanup expired sessions');
    }
  }
}
