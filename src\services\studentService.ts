// src/services/studentService.ts
import { DATABASE_TABLES } from '../config/database';
import { supabase } from '../lib/supabase';
import type { Student, StudentInsert, StudentUpdate } from '../types/database';

export interface StudentWithRelations extends Student {
  class: { id: string; name: string } | null;
  section: { id: string; name: string } | null;
  academic_year: { id: string; year: string } | null;
  guardian_relation: { id: string; name: string } | null;
}

export interface CreateStudentData {
  // Personal Information
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  gender: 'male' | 'female' | 'other';
  email?: string;
  phoneNumber?: string;
  address?: string;

  // Guardian Information
  guardianName: string;
  guardianRelationId: string;
  guardianPhone: string;
  guardianEmail?: string;
  guardianAddress?: string;
  emergencyContact?: string;

  // Academic Information
  classId: string;
  sectionId: string;
  rollNumber: string;
  previousSchool?: string;
  academicYearId: string;

  // Documents (URLs after upload)
  birthCertificateUrl?: string;
  previousRecordsUrl?: string;
  medicalRecordsUrl?: string;
  photographUrl?: string;
}

export class StudentService {
  /**
   * Create a new student
   */
  static async createStudent(studentData: CreateStudentData): Promise<Student> {
    const studentInsert: StudentInsert = {
      first_name: studentData.firstName,
      last_name: studentData.lastName,
      date_of_birth: studentData.dateOfBirth,
      gender: studentData.gender,
      email: studentData.email || null,
      phone_number: studentData.phoneNumber || null,
      address: studentData.address || null,
      guardian_name: studentData.guardianName,
      guardian_relation_id: studentData.guardianRelationId,
      guardian_phone: studentData.guardianPhone,
      guardian_email: studentData.guardianEmail || null,
      guardian_address: studentData.guardianAddress || null,
      emergency_contact: studentData.emergencyContact || null,
      class_id: studentData.classId,
      section_id: studentData.sectionId,
      roll_number: studentData.rollNumber,
      previous_school: studentData.previousSchool || null,
      academic_year_id: studentData.academicYearId,
      birth_certificate_url: studentData.birthCertificateUrl || null,
      previous_records_url: studentData.previousRecordsUrl || null,
      medical_records_url: studentData.medicalRecordsUrl || null,
      photograph_url: studentData.photographUrl || null,
      is_active: true
    };    const { data, error } = await supabase
      .from(DATABASE_TABLES.STUDENTS)
      .insert(studentInsert)
      .select()
      .single();

    if (error) {
      console.error('Error creating student:', error);
      throw new Error(`Failed to create student: ${error.message}`);
    }

    return data;
  }
  /**
   * Get all students with relations
   */
  static async getAllStudents(): Promise<StudentWithRelations[]> {
    const { data, error } = await supabase
      .from(DATABASE_TABLES.STUDENTS)
      .select(`
        *,
        class:${DATABASE_TABLES.CLASSES}(id, name),
        section:${DATABASE_TABLES.SECTIONS}(id, name),
        academic_year:${DATABASE_TABLES.ACADEMIC_YEARS}(id, year),
        guardian_relation:${DATABASE_TABLES.GUARDIAN_RELATIONS}(id, name)
      `)
      .eq('is_active', true)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching students:', error);
      throw new Error(`Failed to fetch students: ${error.message}`);
    }

    return data as StudentWithRelations[] || [];
  }
  /**
   * Get student by ID with relations
   */
  static async getStudentById(id: string): Promise<StudentWithRelations | null> {
    const { data, error } = await supabase
      .from(DATABASE_TABLES.STUDENTS)
      .select(`
        *,
        class:${DATABASE_TABLES.CLASSES}(id, name),
        section:${DATABASE_TABLES.SECTIONS}(id, name),
        academic_year:${DATABASE_TABLES.ACADEMIC_YEARS}(id, year),
        guardian_relation:${DATABASE_TABLES.GUARDIAN_RELATIONS}(id, name)
      `)
      .eq('id', id)
      .eq('is_active', true)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null;
      }
      console.error('Error fetching student:', error);
      throw new Error(`Failed to fetch student: ${error.message}`);
    }

    return data as StudentWithRelations;
  }

  /**
   * Update student
   */
  static async updateStudent(id: string, updateData: Partial<CreateStudentData>): Promise<Student> {
    const studentUpdate: StudentUpdate = {};

    // Map the update data to database fields
    if (updateData.firstName !== undefined) studentUpdate.first_name = updateData.firstName;
    if (updateData.lastName !== undefined) studentUpdate.last_name = updateData.lastName;
    if (updateData.dateOfBirth !== undefined) studentUpdate.date_of_birth = updateData.dateOfBirth;
    if (updateData.gender !== undefined) studentUpdate.gender = updateData.gender;
    if (updateData.email !== undefined) studentUpdate.email = updateData.email || null;
    if (updateData.phoneNumber !== undefined) studentUpdate.phone_number = updateData.phoneNumber || null;
    if (updateData.address !== undefined) studentUpdate.address = updateData.address || null;
    if (updateData.guardianName !== undefined) studentUpdate.guardian_name = updateData.guardianName;
    if (updateData.guardianRelationId !== undefined) studentUpdate.guardian_relation_id = updateData.guardianRelationId;
    if (updateData.guardianPhone !== undefined) studentUpdate.guardian_phone = updateData.guardianPhone;
    if (updateData.guardianEmail !== undefined) studentUpdate.guardian_email = updateData.guardianEmail || null;
    if (updateData.guardianAddress !== undefined) studentUpdate.guardian_address = updateData.guardianAddress || null;
    if (updateData.emergencyContact !== undefined) studentUpdate.emergency_contact = updateData.emergencyContact || null;
    if (updateData.classId !== undefined) studentUpdate.class_id = updateData.classId;
    if (updateData.sectionId !== undefined) studentUpdate.section_id = updateData.sectionId;
    if (updateData.rollNumber !== undefined) studentUpdate.roll_number = updateData.rollNumber;
    if (updateData.previousSchool !== undefined) studentUpdate.previous_school = updateData.previousSchool || null;
    if (updateData.academicYearId !== undefined) studentUpdate.academic_year_id = updateData.academicYearId;
    if (updateData.birthCertificateUrl !== undefined) studentUpdate.birth_certificate_url = updateData.birthCertificateUrl || null;
    if (updateData.previousRecordsUrl !== undefined) studentUpdate.previous_records_url = updateData.previousRecordsUrl || null;
    if (updateData.medicalRecordsUrl !== undefined) studentUpdate.medical_records_url = updateData.medicalRecordsUrl || null;
    if (updateData.photographUrl !== undefined) studentUpdate.photograph_url = updateData.photographUrl || null;

    // Always update the updated_at timestamp
    studentUpdate.updated_at = new Date().toISOString();    const { data, error } = await supabase
      .from(DATABASE_TABLES.STUDENTS)
      .update(studentUpdate)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating student:', error);
      throw new Error(`Failed to update student: ${error.message}`);
    }

    return data;
  }

  /**
   * Delete student (soft delete)
   */
  static async deleteStudent(id: string): Promise<void> {
    const { error } = await supabase
      .from(DATABASE_TABLES.STUDENTS)
      .update({ is_active: false, updated_at: new Date().toISOString() })
      .eq('id', id);

    if (error) {
      console.error('Error deleting student:', error);
      throw new Error(`Failed to delete student: ${error.message}`);
    }
  }

  /**
   * Search students by name, roll number, or class
   */  static async searchStudents(query: string): Promise<StudentWithRelations[]> {
    const { data, error } = await supabase
      .from(DATABASE_TABLES.STUDENTS)
      .select(`
        *,
        class:${DATABASE_TABLES.CLASSES}(id, name),
        section:${DATABASE_TABLES.SECTIONS}(id, name),
        academic_year:${DATABASE_TABLES.ACADEMIC_YEARS}(id, year),
        guardian_relation:${DATABASE_TABLES.GUARDIAN_RELATIONS}(id, name)
      `)
      .or(`first_name.ilike.%${query}%,last_name.ilike.%${query}%,roll_number.ilike.%${query}%`)
      .eq('is_active', true)
      .order('first_name');

    if (error) {
      console.error('Error searching students:', error);
      throw new Error(`Failed to search students: ${error.message}`);
    }

    return data as StudentWithRelations[] || [];
  }

  /**
   * Get students by class and section
   */
  static async getStudentsByClassAndSection(
    classId: string, 
    sectionId: string, 
    academicYearId?: string
  ): Promise<StudentWithRelations[]> {    let query = supabase
      .from(DATABASE_TABLES.STUDENTS)
      .select(`
        *,
        class:${DATABASE_TABLES.CLASSES}(id, name),
        section:${DATABASE_TABLES.SECTIONS}(id, name),
        academic_year:${DATABASE_TABLES.ACADEMIC_YEARS}(id, year),
        guardian_relation:${DATABASE_TABLES.GUARDIAN_RELATIONS}(id, name)
      `)
      .eq('class_id', classId)
      .eq('section_id', sectionId)
      .eq('is_active', true);

    if (academicYearId) {
      query = query.eq('academic_year_id', academicYearId);
    }

    const { data, error } = await query.order('roll_number');

    if (error) {
      console.error('Error fetching students by class and section:', error);
      throw new Error(`Failed to fetch students: ${error.message}`);
    }

    return data as StudentWithRelations[] || [];
  }
}
