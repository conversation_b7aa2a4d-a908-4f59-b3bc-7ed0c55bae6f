// src/components/student-management/student-management.tsx
'use client';

import { useState } from 'react';
import StudentList from './student-list';
import AddStudentWizard from './add-student-wizard';

interface StudentManagementProps {
  activeSubSection?: string;
}

const StudentManagement = ({ activeSubSection = 'Current Students' }: StudentManagementProps) => {
  const [showAddStudentWizard, setShowAddStudentWizard] = useState(false);

  const handleAddNewStudent = () => {
    setShowAddStudentWizard(true);
  };

  const handleCloseWizard = () => {
    setShowAddStudentWizard(false);
  };

  const handleSaveStudent = (studentData: any) => {
    console.log('Saving student:', studentData);
    // Here you would typically save to your backend/database
    setShowAddStudentWizard(false);
  };

  const renderContent = () => {
    switch (activeSubSection) {
      case 'Current Students':
        return <StudentList onAddNewStudent={handleAddNewStudent} />;
      
      case 'Enroll Student':
        return (
          <div className="p-6 bg-gray-100 min-h-full">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
              <div className="max-w-md mx-auto">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Enroll New Student</h3>
                <p className="text-gray-600 mb-6">Start the enrollment process for a new student by clicking the button below.</p>
                <button
                  onClick={handleAddNewStudent}
                  className="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center space-x-2 mx-auto"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                  </svg>
                  <span>Start Enrollment</span>
                </button>
              </div>
            </div>
          </div>
        );
      
      case 'Class Assignment':
        return (
          <div className="p-6 bg-gray-100 min-h-full">
            <div className="bg-gradient-to-r from-indigo-50 to-blue-50 rounded-lg p-6 mb-6 border border-indigo-100">
              <div className="flex items-center justify-between">
                <div>
                  <h1 className="text-2xl font-bold text-gray-900 mb-2">Class Assignment</h1>
                  <p className="text-gray-600">Manage student class and section assignments.</p>
                </div>
              </div>
            </div>
            
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
              <div className="max-w-md mx-auto">
                <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Class Assignment Management</h3>
                <p className="text-gray-600">This feature will allow you to assign students to classes and sections, manage class capacity, and handle transfers between classes.</p>
              </div>
            </div>
          </div>
        );
      
      case 'School Records':
        return (
          <div className="p-6 bg-gray-100 min-h-full">
            <div className="bg-gradient-to-r from-indigo-50 to-blue-50 rounded-lg p-6 mb-6 border border-indigo-100">
              <div className="flex items-center justify-between">
                <div>
                  <h1 className="text-2xl font-bold text-gray-900 mb-2">School Records</h1>
                  <p className="text-gray-600">Access and manage student academic records and documents.</p>
                </div>
              </div>
            </div>
            
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
              <div className="max-w-md mx-auto">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">School Records Management</h3>
                <p className="text-gray-600">This feature will provide access to student transcripts, report cards, attendance records, and other important academic documents.</p>
              </div>
            </div>
          </div>
        );
      
      default:
        return <StudentList onAddNewStudent={handleAddNewStudent} />;
    }
  };

  return (
    <>
      {renderContent()}
      
      {/* Add Student Wizard Modal */}
      {showAddStudentWizard && (
        <AddStudentWizard
          onClose={handleCloseWizard}
          onSave={handleSaveStudent}
        />
      )}
    </>
  );
};

export default StudentManagement;
