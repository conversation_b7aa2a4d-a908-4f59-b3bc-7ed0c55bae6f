// src/services/enrollment/validationService.ts
import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '../../types/database';
import { BaseService } from '../core/baseService';
import { ServiceError, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>r<PERSON><PERSON><PERSON> } from '../core/errorHandler';
import { EnrollmentStep } from './enrollmentService';
import { StudentEnrollmentData } from '../student/enhancedStudentService';

/**
 * Validation result interface
 */
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Field validation rule
 */
export interface ValidationRule {
  field: string;
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  customValidator?: (value: any) => string | null;
}

/**
 * Validation service for enrollment data
 */
export class ValidationService extends BaseService {
  constructor(client: SupabaseClient<Database>) {
    super(client);
  }

  /**
   * Validate a specific enrollment step
   */
  async validateStep(step: EnrollmentStep, data: any): Promise<ValidationResult> {
    try {
      switch (step) {
        case EnrollmentStep.PERSONAL_INFO:
          return this.validatePersonalInfo(data);
        case EnrollmentStep.GUARDIAN_DETAILS:
          return this.validateGuardianDetails(data);
        case EnrollmentStep.ACADEMIC_INFO:
          return await this.validateAcademicInfo(data);
        case EnrollmentStep.DOCUMENTS:
          return this.validateDocuments(data);
        default:
          return { isValid: true, errors: [], warnings: [] };
      }
    } catch (error) {
      throw ErrorHandler.handle(error, `Validate ${step}`);
    }
  }

  /**
   * Validate complete enrollment data
   */
  async validateCompleteEnrollment(data: Partial<StudentEnrollmentData>): Promise<ValidationResult> {
    try {
      const errors: string[] = [];
      const warnings: string[] = [];

      // Validate all steps
      if (data.student) {
        const personalResult = await this.validatePersonalInfo(data.student);
        errors.push(...personalResult.errors);
        warnings.push(...personalResult.warnings);
      } else {
        errors.push('Personal information is required');
      }

      if (data.guardian) {
        const guardianResult = await this.validateGuardianDetails(data.guardian);
        errors.push(...guardianResult.errors);
        warnings.push(...guardianResult.warnings);
      } else {
        errors.push('Guardian information is required');
      }

      if (data.academic) {
        const academicResult = await this.validateAcademicInfo(data.academic);
        errors.push(...academicResult.errors);
        warnings.push(...academicResult.warnings);
      } else {
        errors.push('Academic information is required');
      }

      if (data.documents) {
        const documentsResult = this.validateDocuments(data.documents);
        errors.push(...documentsResult.errors);
        warnings.push(...documentsResult.warnings);
      }

      return {
        isValid: errors.length === 0,
        errors,
        warnings
      };
    } catch (error) {
      throw ErrorHandler.handle(error, 'Validate complete enrollment');
    }
  }

  /**
   * Validate personal information
   */
  private validatePersonalInfo(data: any): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    const rules: ValidationRule[] = [
      {
        field: 'firstName',
        required: true,
        minLength: 2,
        maxLength: 50,
        pattern: /^[a-zA-Z\s]+$/,
        customValidator: (value) => {
          if (value && value.trim().length < 2) {
            return 'First name must be at least 2 characters long';
          }
          return null;
        }
      },
      {
        field: 'lastName',
        required: true,
        minLength: 2,
        maxLength: 50,
        pattern: /^[a-zA-Z\s]+$/
      },
      {
        field: 'dateOfBirth',
        required: true,
        customValidator: (value) => {
          if (!value) return 'Date of birth is required';
          
          const dob = new Date(value);
          if (isNaN(dob.getTime())) {
            return 'Invalid date format';
          }

          const age = this.calculateAge(dob);
          if (age < 3) {
            return 'Student must be at least 3 years old';
          }
          if (age > 25) {
            return 'Student must be under 25 years old';
          }

          return null;
        }
      },
      {
        field: 'gender',
        required: true,
        customValidator: (value) => {
          const validGenders = ['male', 'female', 'other'];
          if (!validGenders.includes(value?.toLowerCase())) {
            return 'Please select a valid gender';
          }
          return null;
        }
      },
      {
        field: 'email',
        required: false,
        pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        customValidator: (value) => {
          if (value && !this.isValidEmail(value)) {
            return 'Please enter a valid email address';
          }
          return null;
        }
      },
      {
        field: 'phoneNumber',
        required: false,
        pattern: /^[\+]?[1-9][\d]{0,15}$/,
        customValidator: (value) => {
          if (value && !this.isValidPhone(value)) {
            return 'Please enter a valid phone number';
          }
          return null;
        }
      }
    ];

    // Apply validation rules
    for (const rule of rules) {
      const fieldErrors = this.validateField(data, rule);
      errors.push(...fieldErrors);
    }

    // Additional business logic validations
    if (data.email && data.phoneNumber) {
      // Both email and phone provided - good for communication
    } else if (!data.email && !data.phoneNumber) {
      warnings.push('Consider providing either email or phone number for better communication');
    }

    return { isValid: errors.length === 0, errors, warnings };
  }

  /**
   * Validate guardian details
   */
  private validateGuardianDetails(data: any): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    const rules: ValidationRule[] = [
      {
        field: 'name',
        required: true,
        minLength: 2,
        maxLength: 100,
        pattern: /^[a-zA-Z\s]+$/
      },
      {
        field: 'relationId',
        required: true,
        customValidator: (value) => {
          if (!value || value.trim() === '') {
            return 'Please select guardian relationship';
          }
          return null;
        }
      },
      {
        field: 'phone',
        required: true,
        pattern: /^[\+]?[1-9][\d]{0,15}$/,
        customValidator: (value) => {
          if (!this.isValidPhone(value)) {
            return 'Please enter a valid phone number';
          }
          return null;
        }
      },
      {
        field: 'email',
        required: false,
        pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      },
      {
        field: 'emergencyContact',
        required: false,
        pattern: /^[\+]?[1-9][\d]{0,15}$/,
        customValidator: (value) => {
          if (value && !this.isValidPhone(value)) {
            return 'Please enter a valid emergency contact number';
          }
          return null;
        }
      }
    ];

    // Apply validation rules
    for (const rule of rules) {
      const fieldErrors = this.validateField(data, rule);
      errors.push(...fieldErrors);
    }

    // Business logic validations
    if (data.phone === data.emergencyContact) {
      warnings.push('Emergency contact should be different from primary phone number');
    }

    if (!data.email) {
      warnings.push('Consider providing guardian email for important communications');
    }

    return { isValid: errors.length === 0, errors, warnings };
  }

  /**
   * Validate academic information
   */
  private async validateAcademicInfo(data: any): Promise<ValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    const rules: ValidationRule[] = [
      {
        field: 'classId',
        required: true,
        customValidator: (value) => {
          if (!value || value.trim() === '') {
            return 'Please select a class';
          }
          return null;
        }
      },
      {
        field: 'sectionId',
        required: true,
        customValidator: (value) => {
          if (!value || value.trim() === '') {
            return 'Please select a section';
          }
          return null;
        }
      },
      {
        field: 'academicYearId',
        required: true,
        customValidator: (value) => {
          if (!value || value.trim() === '') {
            return 'Please select an academic year';
          }
          return null;
        }
      },
      {
        field: 'rollNumber',
        required: true,
        minLength: 1,
        maxLength: 20,
        pattern: /^[A-Za-z0-9]+$/,
        customValidator: (value) => {
          if (!value || value.trim() === '') {
            return 'Roll number is required';
          }
          if (!/^[A-Za-z0-9]+$/.test(value)) {
            return 'Roll number can only contain letters and numbers';
          }
          return null;
        }
      },
      {
        field: 'admissionDate',
        required: true,
        customValidator: (value) => {
          if (!value) return 'Admission date is required';
          
          const admissionDate = new Date(value);
          if (isNaN(admissionDate.getTime())) {
            return 'Invalid admission date format';
          }

          const today = new Date();
          if (admissionDate > today) {
            return 'Admission date cannot be in the future';
          }

          return null;
        }
      }
    ];

    // Apply validation rules
    for (const rule of rules) {
      const fieldErrors = this.validateField(data, rule);
      errors.push(...fieldErrors);
    }

    // Additional async validations
    if (data.rollNumber && data.classId && data.sectionId && data.academicYearId) {
      try {
        // Check roll number uniqueness
        const { data: existingStudent, error } = await this.client
          .from('academic_records' as any)
          .select('student_id')
          .eq('roll_number', data.rollNumber)
          .eq('class_id', data.classId)
          .eq('section_id', data.sectionId)
          .eq('academic_year_id', data.academicYearId)
          .eq('is_active', true)
          .single();

        if (!error && existingStudent) {
          errors.push('This roll number is already taken in the selected class and section');
        }
      } catch (error) {
        // If error is "not found", roll number is available
        if (error instanceof Error && !error.message.includes('PGRST116')) {
          warnings.push('Could not verify roll number uniqueness. Please ensure it is unique.');
        }
      }
    }

    return { isValid: errors.length === 0, errors, warnings };
  }

  /**
   * Validate documents
   */
  private validateDocuments(data: any): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!Array.isArray(data)) {
      return { isValid: true, errors: [], warnings: ['No documents provided'] };
    }

    const requiredDocumentTypes = ['birth_certificate', 'photograph'];
    const providedTypes = data.map(doc => doc.type);

    for (const requiredType of requiredDocumentTypes) {
      if (!providedTypes.includes(requiredType)) {
        errors.push(`${requiredType.replace('_', ' ')} is required`);
      }
    }

    // Validate each document
    for (const doc of data) {
      if (!doc.file || doc.file.size === 0) {
        errors.push(`File is required for ${doc.type}`);
        continue;
      }

      // File size validation (5MB limit)
      if (doc.file.size > 5 * 1024 * 1024) {
        errors.push(`File size for ${doc.type} must be less than 5MB`);
      }

      // File type validation
      const allowedTypes = ['image/jpeg', 'image/png', 'application/pdf'];
      if (!allowedTypes.includes(doc.file.type)) {
        errors.push(`Invalid file type for ${doc.type}. Allowed: JPEG, PNG, PDF`);
      }
    }

    if (data.length === 0) {
      warnings.push('Consider uploading required documents to complete the enrollment');
    }

    return { isValid: errors.length === 0, errors, warnings };
  }

  /**
   * Validate a single field against rules
   */
  private validateField(data: any, rule: ValidationRule): string[] {
    const errors: string[] = [];
    const value = data[rule.field];

    // Required field validation
    if (rule.required && (value === undefined || value === null || value === '')) {
      errors.push(`${this.formatFieldName(rule.field)} is required`);
      return errors;
    }

    // Skip other validations if field is empty and not required
    if (!rule.required && (value === undefined || value === null || value === '')) {
      return errors;
    }

    // Length validations
    if (rule.minLength && value.length < rule.minLength) {
      errors.push(`${this.formatFieldName(rule.field)} must be at least ${rule.minLength} characters long`);
    }

    if (rule.maxLength && value.length > rule.maxLength) {
      errors.push(`${this.formatFieldName(rule.field)} must be no more than ${rule.maxLength} characters long`);
    }

    // Pattern validation
    if (rule.pattern && !rule.pattern.test(value)) {
      errors.push(`${this.formatFieldName(rule.field)} has invalid format`);
    }

    // Custom validation
    if (rule.customValidator) {
      const customError = rule.customValidator(value);
      if (customError) {
        errors.push(customError);
      }
    }

    return errors;
  }

  /**
   * Format field name for display
   */
  private formatFieldName(fieldName: string): string {
    return fieldName
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, str => str.toUpperCase())
      .trim();
  }

  /**
   * Calculate age from date of birth
   */
  private calculateAge(dateOfBirth: Date): number {
    const today = new Date();
    let age = today.getFullYear() - dateOfBirth.getFullYear();
    const monthDiff = today.getMonth() - dateOfBirth.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < dateOfBirth.getDate())) {
      age--;
    }
    
    return age;
  }

  /**
   * Validate email format
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate phone number format
   */
  private isValidPhone(phone: string): boolean {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
  }
}
