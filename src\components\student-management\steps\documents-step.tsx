// src/components/student-management/steps/documents-step.tsx
'use client';

import React from 'react';

interface DocumentsStepProps {
  data: any;
  validationErrors: string[];
  onComplete: (data: any) => void;
  onBack?: () => void;
  submitting: boolean;
}

const DocumentsStep: React.FC<DocumentsStepProps> = ({
  data,
  validationErrors,
  onComplete,
  onBack,
  submitting
}) => {
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Mock documents data
    onComplete([]);
  };

  return (
    <div>
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Documents Upload</h2>
        <p className="text-gray-600">Upload required documents for enrollment</p>
      </div>

      {validationErrors.length > 0 && (
        <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
          <h3 className="text-sm font-medium text-red-800 mb-2">Please fix the following errors:</h3>
          <ul className="list-disc list-inside text-sm text-red-700">
            {validationErrors.map((error, index) => (
              <li key={index}>{error}</li>
            ))}
          </ul>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <p className="text-blue-800 text-sm">
            This is a placeholder component. The full document upload interface will be implemented here.
          </p>
        </div>

        <div className="flex justify-between pt-6">
          <button
            type="button"
            onClick={onBack}
            disabled={submitting}
            className="px-6 py-2 text-gray-600 hover:text-gray-800 transition-colors disabled:opacity-50"
          >
            Back
          </button>

          <button
            type="submit"
            disabled={submitting}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center space-x-2"
          >
            {submitting && (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            )}
            <span>Continue</span>
          </button>
        </div>
      </form>
    </div>
  );
};

export default DocumentsStep;
