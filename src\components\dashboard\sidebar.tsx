'use client';

import { useEffect, useState } from 'react';

interface SidebarProps {
  activeItem?: string;
  onItemClick?: (item: string) => void;
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
}

const Sidebar = ({ activeItem = 'Dashboard', onItemClick, isCollapsed = false, onToggleCollapse }: SidebarProps) => {
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);
  const [expandedItems, setExpandedItems] = useState<string[]>(['Student Management']);

  // Listen for navigation changes to close sub-navigation items
  useEffect(() => {
    const handleNavigationChange = (event: CustomEvent) => {
      const { section } = event.detail;
      // Close all expanded items if navigating to a different main section
      const mainSections = ['Dashboard', 'Staff Management', 'Attendance Management', 'Leave Management', 'Academic Management', 'Fee Management', 'Library Management', 'Transport Management', 'Reports', 'Profile'];
      if (mainSections.includes(section)) {
        // Only keep the current section expanded if it has sub-items (Student Management has sub-items)
        if (section === 'Student Management') {
          setExpandedItems([section]);
        } else {
          setExpandedItems([]);
        }
      }
    };

    window.addEventListener('navigationChange', handleNavigationChange as EventListener);
    return () => window.removeEventListener('navigationChange', handleNavigationChange as EventListener);
  }, []);

  const menuItems = [
    {
      id: 'Dashboard',
      name: 'Dashboard',
      icon: (
        <svg className={`${isCollapsed ? 'w-6 h-6' : 'w-5 h-5'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v4H8V5z" />
        </svg>
      ),
    },
    {
      id: 'Student Management',
      name: 'Student Management',
      icon: (
        <svg className={`${isCollapsed ? 'w-6 h-6' : 'w-5 h-5'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
        </svg>
      ),
      hasSubItems: true,
      subItems: [
        {
          id: 'Current Students',
          name: 'Current Students',
          icon: (
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          ),
        },
        {
          id: 'Class Assignment',
          name: 'Class Assignment',
          icon: (
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
            </svg>
          ),
        },
        {
          id: 'School Records',
          name: 'School Records',
          icon: (
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          ),
        },
      ],
    },
    {
      id: 'Staff Management',
      name: 'Staff Management',
      icon: (
        <svg className={`${isCollapsed ? 'w-6 h-6' : 'w-5 h-5'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 515.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 919.288 0M15 7a3 3 0 11-6 0 3 3 0 616 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
        </svg>
      ),
    },
    {
      id: 'Attendance Management',
      name: 'Attendance Management',
      icon: (
        <svg className={`${isCollapsed ? 'w-6 h-6' : 'w-5 h-5'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
        </svg>
      ),
    },
    {
      id: 'Leave Management',
      name: 'Leave Management',
      icon: (
        <svg className={`${isCollapsed ? 'w-6 h-6' : 'w-5 h-5'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
      ),
    },
    {
      id: 'Academic Management',
      name: 'Academic Management',
      icon: (
        <svg className={`${isCollapsed ? 'w-6 h-6' : 'w-5 h-5'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 14l9-5-9-5-9 5 9 5z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z" />
        </svg>
      ),
    },
    {
      id: 'Fee Management',
      name: 'Fee Management',
      icon: (
        <svg className={`${isCollapsed ? 'w-6 h-6' : 'w-5 h-5'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
    },
    {
      id: 'Library Management',
      name: 'Library Management',
      icon: (
        <svg className={`${isCollapsed ? 'w-6 h-6' : 'w-5 h-5'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 14v3m4-3v3m4-3v3M3 21h18M3 10h18M3 7l9-4 9 4M4 10h16v11H4V10z" />
        </svg>
      ),
    },
    {
      id: 'Transport Management',
      name: 'Transport Management',
      icon: (
        <svg className={`${isCollapsed ? 'w-6 h-6' : 'w-5 h-5'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
    },
    {
      id: 'Reports',
      name: 'Reports',
      icon: (
        <svg className={`${isCollapsed ? 'w-6 h-6' : 'w-5 h-5'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      ),
    },
  ];

  const handleItemClick = (item: string) => {
    if (onItemClick) {
      onItemClick(item);
    }
  };

  const toggleExpanded = (itemId: string) => {
    setExpandedItems((prev: string[]) =>
      prev.includes(itemId)
        ? prev.filter((id: string) => id !== itemId)
        : [...prev, itemId]
    );
  };

  const isExpanded = (itemId: string) => expandedItems.includes(itemId);

  return (
    <div className="relative">
      <div className={`${isCollapsed ? 'w-16' : 'w-64'} h-screen flex flex-col transition-all duration-300 fixed left-0 top-0 z-40 bg-gray-900 border-r border-gray-700 shadow-2xl overflow-visible`}>
        
        {/* Header Section */}
        <div className={`h-14 flex items-center ${isCollapsed ? 'px-4 justify-center' : 'px-6'} bg-gray-900 border-b border-gray-700`}>
          <div className={`flex items-center ${isCollapsed ? 'justify-center' : 'space-x-3'} w-full`}>
            <div className={`${isCollapsed ? 'w-8 h-8' : 'w-10 h-10'} bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg transition-all duration-300 ring-2 ring-blue-500/20`}>
              <svg className={`${isCollapsed ? 'w-4 h-4' : 'w-5 h-5'} text-white`} fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z"/>
              </svg>
            </div>
            {!isCollapsed && (
              <div className="flex flex-col">
                <span className="text-base font-bold text-white">EduPro</span>
                <span className="text-base text-gray-400 font-normal opacity-75">Education Platform</span>
              </div>
            )}
          </div>
        </div>

        {/* Navigation Menu */}
        <nav className={`flex-1 ${isCollapsed ? 'px-2' : 'px-4'} py-6 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-gray-800 overflow-x-visible`}>
          <div className="space-y-2">
            {menuItems.map((item) => (
              <div key={item.id} className="group">
                {/* Main Menu Item */}
                <div className="relative overflow-visible">
                  <button
                    data-menu-item={item.id}
                    onClick={() => {
                      if (isCollapsed) {
                        if (onToggleCollapse) {
                          onToggleCollapse();
                        }
                        setTimeout(() => {
                          if (item.hasSubItems) {
                            toggleExpanded(item.id);
                          } else {
                            handleItemClick(item.id);
                          }
                        }, 150);
                      } else {
                        if (item.hasSubItems) {
                          toggleExpanded(item.id);
                        } else {
                          handleItemClick(item.id);
                        }
                      }
                    }}
                    onMouseEnter={() => setHoveredItem(item.id)}
                    onMouseLeave={() => setHoveredItem(null)}
                    className={`w-full flex items-center ${isCollapsed ? 'px-3 py-4 justify-center' : 'px-3 py-2.5'} text-base font-medium rounded-lg transition-all duration-200 relative group ${
                      activeItem === item.id || (item.hasSubItems && item.subItems?.some(sub => activeItem === sub.id))
                        ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg shadow-blue-500/25 border border-blue-500/30'
                        : hoveredItem === item.id
                        ? 'bg-gradient-to-r from-gray-700 to-gray-600 text-white border border-gray-500 shadow-md transform scale-[1.02]'
                        : 'text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-gray-800 hover:to-gray-700 border border-transparent hover:border-gray-600'
                    }`}
                  >
                    {/* Active indicator */}
                    {(activeItem === item.id || (item.hasSubItems && item.subItems?.some(sub => activeItem === sub.id))) && !isCollapsed && (
                      <div className="absolute left-0 top-1/2 transform -translate-y-1/2 w-1 h-6 bg-white rounded-r-full shadow-sm"></div>
                    )}

                    <span className={`${!isCollapsed ? 'mr-3' : ''} transition-colors duration-200 ${
                      activeItem === item.id || (item.hasSubItems && item.subItems?.some(sub => activeItem === sub.id))
                        ? 'text-white drop-shadow-sm'
                        : hoveredItem === item.id
                        ? 'text-white'
                        : 'text-gray-400 group-hover:text-white'
                    }`}>
                      {item.icon}
                    </span>

                    {!isCollapsed && (
                      <>
                        <span className="truncate flex-1 text-left">{item.name}</span>
                        
                        {item.hasSubItems && (
                          <svg
                            className={`w-4 h-4 transition-transform duration-200 ${isExpanded(item.id) ? 'rotate-90' : ''} ${
                              activeItem === item.id || (item.hasSubItems && item.subItems?.some(sub => activeItem === sub.id))
                                ? 'text-white'
                                : hoveredItem === item.id
                                ? 'text-white'
                                : 'text-gray-400 group-hover:text-white'
                            }`}
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                          </svg>
                        )}
                      </>
                    )}
                  </button>

                  {/* Tooltip for collapsed mode */}
                  {isCollapsed && hoveredItem === item.id && (
                    <div className="absolute left-full ml-2 top-1/2 -translate-y-1/2 bg-gray-900 text-white px-3 py-2 rounded-lg text-sm font-medium whitespace-nowrap z-50 shadow-lg">
                      {item.name}
                      <div className="absolute right-full top-1/2 -translate-y-1/2 border-4 border-transparent border-r-gray-900"></div>
                    </div>
                  )}
                </div>

                {/* Sub-items */}
                {!isCollapsed && item.hasSubItems && isExpanded(item.id) && item.subItems && (
                  <div className="ml-6 mt-1 space-y-1 border-l-2 border-gray-700 pl-4">
                    {item.subItems.map((subItem) => (
                      <div key={subItem.id}>
                        <button
                          onClick={() => handleItemClick(subItem.id)}
                          onMouseEnter={() => setHoveredItem(subItem.id)}
                          onMouseLeave={() => setHoveredItem(null)}
                          className={`w-full flex items-center px-3 py-2 text-base font-medium rounded-md transition-all duration-200 ${
                            activeItem === subItem.id
                              ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg shadow-blue-500/25 border border-blue-500/30'
                              : hoveredItem === subItem.id
                              ? 'bg-gradient-to-r from-gray-700 to-gray-600 text-white border border-gray-500 transform scale-[1.02]'
                              : 'text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-gray-800 hover:to-gray-700 border border-transparent hover:border-gray-600'
                          }`}
                        >
                          <div className={`mr-3 ${
                            activeItem === subItem.id 
                              ? 'text-white' 
                              : hoveredItem === subItem.id
                              ? 'text-white'
                              : 'text-gray-400 group-hover:text-white'
                          }`}>
                            {subItem.icon}
                          </div>
                          <span className="flex-1 text-left">{subItem.name}</span>
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </nav>

        {/* Footer */}
        <div className={`${isCollapsed ? 'px-3' : 'px-6'} py-4 border-t border-gray-700 bg-gray-800/50 backdrop-blur-sm transition-all duration-300`}>
          {isCollapsed ? (
            <div className="flex justify-center">
              <div className="w-2 h-2 bg-emerald-400 rounded-full animate-pulse shadow-sm shadow-emerald-400/50" title="System Online"></div>
            </div>
          ) : (
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-emerald-400 rounded-full animate-pulse shadow-sm shadow-emerald-400/50"></div>
                <span className="text-xs font-medium text-gray-300">System Online</span>
              </div>
              <span className="text-xs text-gray-500 font-medium">v2.1.0</span>
            </div>
          )}
        </div>
      </div>

      {/* Toggle Button */}
      {onToggleCollapse && (
        <button
          onClick={onToggleCollapse}
          className={`fixed top-1/2 -translate-y-1/2 ${isCollapsed ? 'left-12' : 'left-60'} z-50 w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 border-2 border-blue-500/30 hover:border-blue-400/50 rounded-full shadow-lg hover:shadow-xl shadow-blue-500/25 hover:shadow-blue-500/40 transition-all duration-300 flex items-center justify-center text-white group backdrop-blur-sm hover:scale-105`}
          title={isCollapsed ? "Expand Sidebar" : "Collapse Sidebar"}
        >
          <svg 
            className={`w-5 h-5 transition-transform duration-300 ${isCollapsed ? 'rotate-180' : ''} drop-shadow-sm`} 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2.5" d="M15 19l-7-7 7-7" />
          </svg>
        </button>
      )}
    </div>
  );
};

export default Sidebar;
