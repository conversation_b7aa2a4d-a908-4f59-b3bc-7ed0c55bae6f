// src/services/student/academicService.ts
import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '../../types/database';
import { BaseService, IRepository } from '../core/baseService';
import { <PERSON><PERSON>rror, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>rrorHand<PERSON> } from '../core/errorHandler';
import { DATABASE_TABLES } from '../../config/database';

/**
 * Academic record interface
 */
export interface AcademicRecord {
  id?: string;
  studentId: string;
  classId: string;
  sectionId: string;
  academicYearId: string;
  rollNumber: string;
  admissionDate: string;
  previousSchool?: string;
  previousClass?: string;
  previousPercentage?: number;
  transferCertificateNumber?: string;
  isActive: boolean;
  createdAt?: string;
  updatedAt?: string;
}

/**
 * Academic record creation data
 */
export interface CreateAcademicRecordData {
  studentId: string;
  classId: string;
  sectionId: string;
  academicYearId: string;
  rollNumber: string;
  admissionDate: string;
  previousSchool?: string;
  previousClass?: string;
  previousPercentage?: number;
  transferCertificateNumber?: string;
}

/**
 * Academic record with relations
 */
export interface AcademicRecordWithRelations extends AcademicRecord {
  class: { id: string; name: string } | null;
  section: { id: string; name: string } | null;
  academicYear: { id: string; year: string } | null;
}

/**
 * Roll number validation result
 */
export interface RollNumberValidation {
  isValid: boolean;
  isUnique: boolean;
  existingStudentId?: string;
  message?: string;
}

/**
 * Class capacity information
 */
export interface ClassCapacity {
  classId: string;
  sectionId: string;
  maxCapacity: number;
  currentEnrollment: number;
  availableSlots: number;
  isAtCapacity: boolean;
}

/**
 * Academic service for managing student academic records
 */
export class AcademicService extends BaseService {
  constructor(client: SupabaseClient<Database>) {
    super(client);
  }

  /**
   * Create academic record for a student
   */
  async createAcademicRecord(data: CreateAcademicRecordData): Promise<AcademicRecord> {
    try {
      this.log('info', 'Creating academic record', { 
        studentId: data.studentId, 
        rollNumber: data.rollNumber 
      });

      // Validate input data
      this.validateCreateData(data);

      // Check if student exists
      const studentExists = await this.checkStudentExists(data.studentId);
      if (!studentExists) {
        throw new ServiceError(
          ErrorCode.NOT_FOUND,
          'Student not found',
          null,
          'Academic record creation'
        );
      }

      // Validate roll number uniqueness
      const rollNumberValidation = await this.validateRollNumber(
        data.rollNumber,
        data.classId,
        data.sectionId,
        data.academicYearId
      );

      if (!rollNumberValidation.isValid || !rollNumberValidation.isUnique) {
        throw new ServiceError(
          ErrorCode.ROLL_NUMBER_TAKEN,
          rollNumberValidation.message || 'Roll number is not available',
          null,
          'Academic record creation'
        );
      }

      // Check class capacity
      const capacity = await this.getClassCapacity(data.classId, data.sectionId, data.academicYearId);
      if (capacity.isAtCapacity) {
        throw new ServiceError(
          ErrorCode.ENROLLMENT_LIMIT_EXCEEDED,
          'Class section is at full capacity',
          null,
          'Academic record creation',
          `This class section has reached its maximum capacity of ${capacity.maxCapacity} students.`
        );
      }

      // Validate academic year is active
      const isValidAcademicYear = await this.validateAcademicYear(data.academicYearId);
      if (!isValidAcademicYear) {
        throw new ServiceError(
          ErrorCode.INVALID_ACADEMIC_YEAR,
          'Invalid or inactive academic year',
          null,
          'Academic record creation'
        );
      }

      // Prepare academic record data
      const academicRecordInsert = {
        id: this.generateId(),
        student_id: data.studentId,
        class_id: data.classId,
        section_id: data.sectionId,
        academic_year_id: data.academicYearId,
        roll_number: data.rollNumber.trim(),
        admission_date: this.formatDate(data.admissionDate),
        previous_school: data.previousSchool?.trim() || null,
        previous_class: data.previousClass?.trim() || null,
        previous_percentage: data.previousPercentage || null,
        transfer_certificate_number: data.transferCertificateNumber?.trim() || null,
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      // Insert academic record
      const academicRecord = await this.executeQuery(
        async () => this.client
          .from('academic_records' as any) // TODO: Add to DATABASE_TABLES
          .insert(academicRecordInsert)
          .select()
          .single(),
        'Create academic record'
      );

      this.log('info', 'Academic record created successfully', { 
        recordId: academicRecord.id,
        studentId: data.studentId 
      });

      return this.mapToAcademicRecord(academicRecord);
    } catch (error) {
      this.log('error', 'Academic record creation failed', { 
        error, 
        studentId: data.studentId 
      });
      throw ErrorHandler.handle(error, 'Create academic record');
    }
  }

  /**
   * Validate roll number uniqueness and format
   */
  async validateRollNumber(
    rollNumber: string,
    classId: string,
    sectionId: string,
    academicYearId: string,
    excludeStudentId?: string
  ): Promise<RollNumberValidation> {
    try {
      // Validate format
      if (!rollNumber || rollNumber.trim() === '') {
        return {
          isValid: false,
          isUnique: false,
          message: 'Roll number is required'
        };
      }

      const trimmedRollNumber = rollNumber.trim();

      // Check format (customize based on your requirements)
      if (!/^[A-Za-z0-9]+$/.test(trimmedRollNumber)) {
        return {
          isValid: false,
          isUnique: false,
          message: 'Roll number can only contain letters and numbers'
        };
      }

      // Check uniqueness within the same class, section, and academic year
      let query = this.client
        .from('academic_records' as any)
        .select('student_id')
        .eq('roll_number', trimmedRollNumber)
        .eq('class_id', classId)
        .eq('section_id', sectionId)
        .eq('academic_year_id', academicYearId)
        .eq('is_active', true);

      if (excludeStudentId) {
        query = query.neq('student_id', excludeStudentId);
      }

      const { data, error } = await query.single();

      if (error && error.code !== 'PGRST116') {
        throw ErrorHandler.handle(error, 'Validate roll number');
      }

      const isUnique = data === null;

      return {
        isValid: true,
        isUnique,
        existingStudentId: data?.student_id,
        message: isUnique ? 'Roll number is available' : 'Roll number is already taken'
      };
    } catch (error) {
      if (error instanceof ServiceError && error.code === ErrorCode.NOT_FOUND) {
        return {
          isValid: true,
          isUnique: true,
          message: 'Roll number is available'
        };
      }
      throw ErrorHandler.handle(error, 'Validate roll number');
    }
  }

  /**
   * Get class capacity information
   */
  async getClassCapacity(
    classId: string,
    sectionId: string,
    academicYearId: string
  ): Promise<ClassCapacity> {
    try {
      // Get section capacity (assuming sections table has max_capacity field)
      const { data: sectionData, error: sectionError } = await this.client
        .from(DATABASE_TABLES.SECTIONS)
        .select('max_capacity')
        .eq('id', sectionId)
        .single();

      if (sectionError) {
        throw ErrorHandler.handle(sectionError, 'Get section capacity');
      }

      const maxCapacity = sectionData?.max_capacity || 50; // Default capacity

      // Count current enrollment
      const { count, error: countError } = await this.client
        .from('academic_records' as any)
        .select('*', { count: 'exact', head: true })
        .eq('class_id', classId)
        .eq('section_id', sectionId)
        .eq('academic_year_id', academicYearId)
        .eq('is_active', true);

      if (countError) {
        throw ErrorHandler.handle(countError, 'Count current enrollment');
      }

      const currentEnrollment = count || 0;
      const availableSlots = Math.max(0, maxCapacity - currentEnrollment);

      return {
        classId,
        sectionId,
        maxCapacity,
        currentEnrollment,
        availableSlots,
        isAtCapacity: availableSlots === 0
      };
    } catch (error) {
      throw ErrorHandler.handle(error, 'Get class capacity');
    }
  }

  /**
   * Get academic record by student ID
   */
  async getAcademicRecordByStudentId(studentId: string): Promise<AcademicRecordWithRelations | null> {
    try {
      const record = await this.executeQueryOptional(
        async () => this.client
          .from('academic_records' as any)
          .select(`
            *,
            class:${DATABASE_TABLES.CLASSES}(id, name),
            section:${DATABASE_TABLES.SECTIONS}(id, name),
            academic_year:${DATABASE_TABLES.ACADEMIC_YEARS}(id, year)
          `)
          .eq('student_id', studentId)
          .eq('is_active', true)
          .single(),
        'Get academic record by student ID'
      );

      return record ? {
        ...this.mapToAcademicRecord(record),
        class: record.class,
        section: record.section,
        academicYear: record.academic_year
      } : null;
    } catch (error) {
      throw ErrorHandler.handle(error, 'Get academic record by student ID');
    }
  }

  /**
   * Update academic record
   */
  async updateAcademicRecord(
    studentId: string,
    data: Partial<CreateAcademicRecordData>
  ): Promise<AcademicRecord> {
    try {
      // If roll number is being updated, validate it
      if (data.rollNumber && data.classId && data.sectionId && data.academicYearId) {
        const rollNumberValidation = await this.validateRollNumber(
          data.rollNumber,
          data.classId,
          data.sectionId,
          data.academicYearId,
          studentId
        );

        if (!rollNumberValidation.isValid || !rollNumberValidation.isUnique) {
          throw new ServiceError(
            ErrorCode.ROLL_NUMBER_TAKEN,
            rollNumberValidation.message || 'Roll number is not available',
            null,
            'Academic record update'
          );
        }
      }

      const updateData = {
        ...data,
        updated_at: new Date().toISOString()
      };

      const record = await this.executeQuery(
        async () => this.client
          .from('academic_records' as any)
          .update(updateData)
          .eq('student_id', studentId)
          .eq('is_active', true)
          .select()
          .single(),
        'Update academic record'
      );

      return this.mapToAcademicRecord(record);
    } catch (error) {
      throw ErrorHandler.handle(error, 'Update academic record');
    }
  }

  /**
   * Get students by class and section
   */
  async getStudentsByClassSection(
    classId: string,
    sectionId: string,
    academicYearId: string
  ): Promise<AcademicRecordWithRelations[]> {
    try {
      const records = await this.executeQuery(
        async () => this.client
          .from('academic_records' as any)
          .select(`
            *,
            class:${DATABASE_TABLES.CLASSES}(id, name),
            section:${DATABASE_TABLES.SECTIONS}(id, name),
            academic_year:${DATABASE_TABLES.ACADEMIC_YEARS}(id, year)
          `)
          .eq('class_id', classId)
          .eq('section_id', sectionId)
          .eq('academic_year_id', academicYearId)
          .eq('is_active', true)
          .order('roll_number'),
        'Get students by class section'
      );

      return records.map(record => ({
        ...this.mapToAcademicRecord(record),
        class: record.class,
        section: record.section,
        academicYear: record.academic_year
      }));
    } catch (error) {
      throw ErrorHandler.handle(error, 'Get students by class section');
    }
  }

  /**
   * Validate academic year is active
   */
  private async validateAcademicYear(academicYearId: string): Promise<boolean> {
    try {
      const { data, error } = await this.client
        .from(DATABASE_TABLES.ACADEMIC_YEARS)
        .select('is_active')
        .eq('id', academicYearId)
        .single();

      if (error) {
        return false;
      }

      return data?.is_active === true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Check if student exists
   */
  private async checkStudentExists(studentId: string): Promise<boolean> {
    try {
      const { data, error } = await this.client
        .from(DATABASE_TABLES.STUDENTS)
        .select('id')
        .eq('id', studentId)
        .eq('is_active', true)
        .single();

      return !error && data !== null;
    } catch (error) {
      return false;
    }
  }

  /**
   * Validate create data
   */
  private validateCreateData(data: CreateAcademicRecordData): void {
    this.validateRequired(data, [
      'studentId',
      'classId',
      'sectionId',
      'academicYearId',
      'rollNumber',
      'admissionDate'
    ]);

    // Validate admission date
    const admissionDate = new Date(data.admissionDate);
    if (isNaN(admissionDate.getTime())) {
      throw new ServiceError(
        ErrorCode.INVALID_FORMAT,
        'Invalid admission date format',
        null,
        'Academic record validation'
      );
    }

    // Validate previous percentage if provided
    if (data.previousPercentage !== undefined) {
      if (data.previousPercentage < 0 || data.previousPercentage > 100) {
        throw new ServiceError(
          ErrorCode.VALIDATION_ERROR,
          'Previous percentage must be between 0 and 100',
          null,
          'Academic record validation'
        );
      }
    }
  }

  /**
   * Map database record to AcademicRecord interface
   */
  private mapToAcademicRecord(record: any): AcademicRecord {
    return {
      id: record.id,
      studentId: record.student_id,
      classId: record.class_id,
      sectionId: record.section_id,
      academicYearId: record.academic_year_id,
      rollNumber: record.roll_number,
      admissionDate: record.admission_date,
      previousSchool: record.previous_school,
      previousClass: record.previous_class,
      previousPercentage: record.previous_percentage,
      transferCertificateNumber: record.transfer_certificate_number,
      isActive: record.is_active,
      createdAt: record.created_at,
      updatedAt: record.updated_at
    };
  }

  /**
   * Delete academic record (soft delete)
   */
  async deleteAcademicRecord(studentId: string): Promise<void> {
    try {
      await this.executeQuery(
        async () => this.client
          .from('academic_records' as any)
          .update({ 
            is_active: false, 
            updated_at: new Date().toISOString() 
          })
          .eq('student_id', studentId),
        'Delete academic record'
      );

      this.log('info', 'Academic record deleted', { studentId });
    } catch (error) {
      throw ErrorHandler.handle(error, 'Delete academic record');
    }
  }
}
