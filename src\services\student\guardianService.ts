// src/services/student/guardianService.ts
import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '../../types/database';
import { BaseService, IRepository } from '../core/baseService';
import { <PERSON>Error, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>rrorHandler } from '../core/errorHandler';
import { DATABASE_TABLES } from '../../config/database';

/**
 * Guardian information interface
 */
export interface GuardianInfo {
  id?: string;
  studentId: string;
  name: string;
  relationId: string;
  phone: string;
  email?: string;
  address?: string;
  emergencyContact?: string;
  occupation?: string;
  workAddress?: string;
  workPhone?: string;
  isPrimary: boolean;
  isActive: boolean;
  createdAt?: string;
  updatedAt?: string;
}

/**
 * Guardian creation data
 */
export interface CreateGuardianData {
  studentId: string;
  name: string;
  relationId: string;
  phone: string;
  email?: string;
  address?: string;
  emergencyContact?: string;
  occupation?: string;
  workAddress?: string;
  workPhone?: string;
  isPrimary?: boolean;
}

/**
 * Guardian update data
 */
export interface UpdateGuardianData {
  name?: string;
  relationId?: string;
  phone?: string;
  email?: string;
  address?: string;
  emergencyContact?: string;
  occupation?: string;
  workAddress?: string;
  workPhone?: string;
  isPrimary?: boolean;
}

/**
 * Guardian with relation details
 */
export interface GuardianWithRelation extends GuardianInfo {
  relation: {
    id: string;
    name: string;
  } | null;
}

/**
 * Guardian repository interface
 */
interface IGuardianRepository extends IRepository<GuardianInfo, CreateGuardianData, UpdateGuardianData> {
  findByStudentId(studentId: string): Promise<GuardianWithRelation[]>;
  findPrimaryGuardian(studentId: string): Promise<GuardianWithRelation | null>;
  setPrimaryGuardian(guardianId: string, studentId: string): Promise<void>;
  validatePhoneUnique(phone: string, excludeId?: string): Promise<boolean>;
}

/**
 * Guardian service for managing student guardians/parents
 */
export class GuardianService extends BaseService implements IGuardianRepository {
  constructor(client: SupabaseClient<Database>) {
    super(client);
  }

  /**
   * Create a new guardian
   */
  async create(data: CreateGuardianData): Promise<GuardianInfo> {
    try {
      this.log('info', 'Creating guardian', { studentId: data.studentId, name: data.name });

      // Validate input data
      this.validateCreateData(data);

      // Check if student exists
      const studentExists = await this.checkStudentExists(data.studentId);
      if (!studentExists) {
        throw new ServiceError(
          ErrorCode.NOT_FOUND,
          'Student not found',
          null,
          'Guardian creation'
        );
      }

      // Validate phone number uniqueness
      const isPhoneUnique = await this.validatePhoneUnique(data.phone);
      if (!isPhoneUnique) {
        throw new ServiceError(
          ErrorCode.DUPLICATE_VALUE,
          'Phone number already exists',
          null,
          'Guardian creation',
          'This phone number is already registered with another guardian.'
        );
      }

      // If this is set as primary, ensure no other primary guardian exists
      if (data.isPrimary) {
        await this.ensureNoPrimaryGuardian(data.studentId);
      }

      // Prepare guardian data for insertion
      const guardianInsert = {
        id: this.generateId(),
        student_id: data.studentId,
        name: data.name.trim(),
        relation_id: data.relationId,
        phone: data.phone.trim(),
        email: data.email?.trim() || null,
        address: data.address?.trim() || null,
        emergency_contact: data.emergencyContact?.trim() || null,
        occupation: data.occupation?.trim() || null,
        work_address: data.workAddress?.trim() || null,
        work_phone: data.workPhone?.trim() || null,
        is_primary: data.isPrimary || false,
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      // Insert guardian
      const guardian = await this.executeQuery(
        async () => this.client
          .from('guardians' as any) // TODO: Add to DATABASE_TABLES
          .insert(guardianInsert)
          .select()
          .single(),
        'Create guardian'
      );

      this.log('info', 'Guardian created successfully', { guardianId: guardian.id });

      return this.mapToGuardianInfo(guardian);
    } catch (error) {
      this.log('error', 'Guardian creation failed', { error, studentId: data.studentId });
      throw ErrorHandler.handle(error, 'Create guardian');
    }
  }

  /**
   * Update guardian information
   */
  async update(id: string, data: UpdateGuardianData): Promise<GuardianInfo> {
    try {
      this.log('info', 'Updating guardian', { guardianId: id });

      // Validate input data
      this.validateUpdateData(data);

      // Check if guardian exists
      const exists = await this.exists(id);
      if (!exists) {
        throw new ServiceError(
          ErrorCode.NOT_FOUND,
          'Guardian not found',
          null,
          'Guardian update'
        );
      }

      // Validate phone number uniqueness if phone is being updated
      if (data.phone) {
        const isPhoneUnique = await this.validatePhoneUnique(data.phone, id);
        if (!isPhoneUnique) {
          throw new ServiceError(
            ErrorCode.DUPLICATE_VALUE,
            'Phone number already exists',
            null,
            'Guardian update',
            'This phone number is already registered with another guardian.'
          );
        }
      }

      // If setting as primary, ensure no other primary guardian exists
      if (data.isPrimary) {
        const guardian = await this.findById(id);
        if (guardian) {
          await this.ensureNoPrimaryGuardian(guardian.studentId, id);
        }
      }

      // Prepare update data
      const updateData = {
        ...data,
        updated_at: new Date().toISOString()
      };

      // Update guardian
      const guardian = await this.executeQuery(
        async () => this.client
          .from('guardians' as any)
          .update(updateData)
          .eq('id', id)
          .select()
          .single(),
        'Update guardian'
      );

      this.log('info', 'Guardian updated successfully', { guardianId: id });

      return this.mapToGuardianInfo(guardian);
    } catch (error) {
      this.log('error', 'Guardian update failed', { error, guardianId: id });
      throw ErrorHandler.handle(error, 'Update guardian');
    }
  }

  /**
   * Find guardian by ID
   */
  async findById(id: string): Promise<GuardianInfo | null> {
    try {
      const guardian = await this.executeQueryOptional(
        async () => this.client
          .from('guardians' as any)
          .select('*')
          .eq('id', id)
          .eq('is_active', true)
          .single(),
        'Find guardian by ID'
      );

      return guardian ? this.mapToGuardianInfo(guardian) : null;
    } catch (error) {
      throw ErrorHandler.handle(error, 'Find guardian by ID');
    }
  }

  /**
   * Find all guardians for a student
   */
  async findByStudentId(studentId: string): Promise<GuardianWithRelation[]> {
    try {
      const guardians = await this.executeQuery(
        async () => this.client
          .from('guardians' as any)
          .select(`
            *,
            relation:${DATABASE_TABLES.GUARDIAN_RELATIONS}(id, name)
          `)
          .eq('student_id', studentId)
          .eq('is_active', true)
          .order('is_primary', { ascending: false }),
        'Find guardians by student ID'
      );

      return guardians.map(guardian => ({
        ...this.mapToGuardianInfo(guardian),
        relation: guardian.relation
      }));
    } catch (error) {
      throw ErrorHandler.handle(error, 'Find guardians by student ID');
    }
  }

  /**
   * Find primary guardian for a student
   */
  async findPrimaryGuardian(studentId: string): Promise<GuardianWithRelation | null> {
    try {
      const guardian = await this.executeQueryOptional(
        async () => this.client
          .from('guardians' as any)
          .select(`
            *,
            relation:${DATABASE_TABLES.GUARDIAN_RELATIONS}(id, name)
          `)
          .eq('student_id', studentId)
          .eq('is_primary', true)
          .eq('is_active', true)
          .single(),
        'Find primary guardian'
      );

      return guardian ? {
        ...this.mapToGuardianInfo(guardian),
        relation: guardian.relation
      } : null;
    } catch (error) {
      throw ErrorHandler.handle(error, 'Find primary guardian');
    }
  }

  /**
   * Set a guardian as primary
   */
  async setPrimaryGuardian(guardianId: string, studentId: string): Promise<void> {
    try {
      // Remove primary status from all guardians for this student
      await this.executeQuery(
        async () => this.client
          .from('guardians' as any)
          .update({ is_primary: false, updated_at: new Date().toISOString() })
          .eq('student_id', studentId),
        'Remove primary status from guardians'
      );

      // Set the specified guardian as primary
      await this.executeQuery(
        async () => this.client
          .from('guardians' as any)
          .update({ is_primary: true, updated_at: new Date().toISOString() })
          .eq('id', guardianId),
        'Set guardian as primary'
      );

      this.log('info', 'Primary guardian set', { guardianId, studentId });
    } catch (error) {
      throw ErrorHandler.handle(error, 'Set primary guardian');
    }
  }

  /**
   * Validate phone number uniqueness
   */
  async validatePhoneUnique(phone: string, excludeId?: string): Promise<boolean> {
    try {
      let query = this.client
        .from('guardians' as any)
        .select('id')
        .eq('phone', phone)
        .eq('is_active', true);

      if (excludeId) {
        query = query.neq('id', excludeId);
      }

      const { data, error } = await query.single();

      if (error && error.code !== 'PGRST116') {
        throw ErrorHandler.handle(error, 'Validate phone uniqueness');
      }

      return data === null;
    } catch (error) {
      if (error instanceof ServiceError && error.code === ErrorCode.NOT_FOUND) {
        return true;
      }
      throw error;
    }
  }

  /**
   * Delete guardian (soft delete)
   */
  async delete(id: string): Promise<void> {
    try {
      await this.executeQuery(
        async () => this.client
          .from('guardians' as any)
          .update({ 
            is_active: false, 
            updated_at: new Date().toISOString() 
          })
          .eq('id', id),
        'Delete guardian'
      );

      this.log('info', 'Guardian deleted', { guardianId: id });
    } catch (error) {
      throw ErrorHandler.handle(error, 'Delete guardian');
    }
  }

  /**
   * Find all guardians (with pagination)
   */
  async findAll(filters?: Record<string, any>): Promise<GuardianInfo[]> {
    try {
      let query = this.client
        .from('guardians' as any)
        .select('*')
        .eq('is_active', true);

      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          query = query.eq(key, value);
        });
      }

      const guardians = await this.executeQuery(
        async () => query,
        'Find all guardians'
      );

      return guardians.map(guardian => this.mapToGuardianInfo(guardian));
    } catch (error) {
      throw ErrorHandler.handle(error, 'Find all guardians');
    }
  }

  /**
   * Check if guardian exists
   */
  async exists(id: string): Promise<boolean> {
    try {
      const { data, error } = await this.client
        .from('guardians' as any)
        .select('id')
        .eq('id', id)
        .eq('is_active', true)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw ErrorHandler.handle(error, 'Check guardian existence');
      }

      return data !== null;
    } catch (error) {
      if (error instanceof ServiceError && error.code === ErrorCode.NOT_FOUND) {
        return false;
      }
      throw error;
    }
  }

  // Private helper methods

  private validateCreateData(data: CreateGuardianData): void {
    this.validateRequired(data, ['studentId', 'name', 'relationId', 'phone']);

    if (data.email && !this.isValidEmail(data.email)) {
      throw new ServiceError(
        ErrorCode.INVALID_FORMAT,
        'Invalid email format',
        null,
        'Guardian validation'
      );
    }

    if (!this.isValidPhone(data.phone)) {
      throw new ServiceError(
        ErrorCode.INVALID_FORMAT,
        'Invalid phone number format',
        null,
        'Guardian validation'
      );
    }
  }

  private validateUpdateData(data: UpdateGuardianData): void {
    if (data.email && !this.isValidEmail(data.email)) {
      throw new ServiceError(
        ErrorCode.INVALID_FORMAT,
        'Invalid email format',
        null,
        'Guardian validation'
      );
    }

    if (data.phone && !this.isValidPhone(data.phone)) {
      throw new ServiceError(
        ErrorCode.INVALID_FORMAT,
        'Invalid phone number format',
        null,
        'Guardian validation'
      );
    }
  }

  private async checkStudentExists(studentId: string): Promise<boolean> {
    try {
      const { data, error } = await this.client
        .from(DATABASE_TABLES.STUDENTS)
        .select('id')
        .eq('id', studentId)
        .eq('is_active', true)
        .single();

      return !error && data !== null;
    } catch (error) {
      return false;
    }
  }

  private async ensureNoPrimaryGuardian(studentId: string, excludeId?: string): Promise<void> {
    let query = this.client
      .from('guardians' as any)
      .select('id')
      .eq('student_id', studentId)
      .eq('is_primary', true)
      .eq('is_active', true);

    if (excludeId) {
      query = query.neq('id', excludeId);
    }

    const { data, error } = await query.single();

    if (!error && data) {
      throw new ServiceError(
        ErrorCode.BUSINESS_RULE_VIOLATION,
        'Student already has a primary guardian',
        null,
        'Guardian validation',
        'Only one primary guardian is allowed per student.'
      );
    }
  }

  private mapToGuardianInfo(guardian: any): GuardianInfo {
    return {
      id: guardian.id,
      studentId: guardian.student_id,
      name: guardian.name,
      relationId: guardian.relation_id,
      phone: guardian.phone,
      email: guardian.email,
      address: guardian.address,
      emergencyContact: guardian.emergency_contact,
      occupation: guardian.occupation,
      workAddress: guardian.work_address,
      workPhone: guardian.work_phone,
      isPrimary: guardian.is_primary,
      isActive: guardian.is_active,
      createdAt: guardian.created_at,
      updatedAt: guardian.updated_at
    };
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  private isValidPhone(phone: string): boolean {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
  }
}
