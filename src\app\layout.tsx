import type { Metada<PERSON> } from 'next';
import { AuthProvider } from '../components/auth/auth-provider';
import '../globals.css';

export const metadata: Metadata = {
  title: 'EduPro',
  description: 'Next Gen School Management',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body>
        <AuthProvider>
          {children}
        </AuthProvider>
      </body>
    </html>
  );
}
