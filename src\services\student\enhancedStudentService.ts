// src/services/student/enhancedStudentService.ts
import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '../../types/database';
import { BaseService, IRepository } from '../core/baseService';
import { <PERSON><PERSON>rror, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>rrorHandler } from '../core/errorHandler';
import { GuardianService, CreateGuardianData } from './guardianService';
import { AcademicService, CreateAcademicRecordData } from './academicService';
import { DocumentService, DocumentUploadRequest } from '../document/documentService';
import { DATABASE_TABLES } from '../../config/database';
import type { Student, StudentInsert } from '../../types/database';

/**
 * Enhanced student creation data
 */
export interface EnhancedCreateStudentData {
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  gender: 'male' | 'female' | 'other';
  email?: string;
  phoneNumber?: string;
  address?: string;
  bloodGroup?: string;
  nationality?: string;
  religion?: string;
}

/**
 * Complete student enrollment data
 */
export interface StudentEnrollmentData {
  student: EnhancedCreateStudentData;
  guardian: CreateGuardianData;
  academic: CreateAcademicRecordData;
  documents?: DocumentUploadRequest[];
}

/**
 * Student with relations
 */
export interface StudentWithRelations extends Student {
  guardians?: any[];
  academicRecord?: any;
  documents?: any[];
}

/**
 * Enhanced student service with clean architecture and transaction support
 */
export class EnhancedStudentService extends BaseService implements IRepository<Student, EnhancedCreateStudentData, Partial<EnhancedCreateStudentData>> {
  private guardianService: GuardianService;
  private academicService: AcademicService;
  private documentService: DocumentService;

  constructor(client: SupabaseClient<Database>) {
    super(client);
    this.guardianService = new GuardianService(client);
    this.academicService = new AcademicService(client);
    this.documentService = new DocumentService(client);
  }

  /**
   * Create a new student
   */
  async create(data: EnhancedCreateStudentData): Promise<Student> {
    try {
      this.log('info', 'Creating student', { name: `${data.firstName} ${data.lastName}` });

      // Validate input data
      this.validateCreateData(data);

      // Check email uniqueness if provided
      if (data.email) {
        const isEmailUnique = await this.isEmailUnique(data.email);
        if (!isEmailUnique) {
          throw new ServiceError(
            ErrorCode.DUPLICATE_VALUE,
            'Email address already exists',
            null,
            'Student creation',
            'This email address is already registered with another student.'
          );
        }
      }

      // Prepare student data for insertion
      const studentInsert: StudentInsert = {
        first_name: data.firstName.trim(),
        last_name: data.lastName.trim(),
        date_of_birth: data.dateOfBirth,
        gender: data.gender,
        email: data.email?.trim() || null,
        phone_number: data.phoneNumber?.trim() || null,
        address: data.address?.trim() || null,
        blood_group: data.bloodGroup?.trim() || null,
        nationality: data.nationality?.trim() || null,
        religion: data.religion?.trim() || null,
        is_active: true,
        // Required fields for existing schema
        guardian_name: '',
        guardian_relation_id: '',
        guardian_phone: '',
        class_id: '',
        section_id: '',
        roll_number: '',
        academic_year_id: ''
      };

      // Insert student
      const student = await this.executeQuery(
        async () => this.client
          .from(DATABASE_TABLES.STUDENTS)
          .insert(studentInsert)
          .select()
          .single(),
        'Create student'
      );

      this.log('info', 'Student created successfully', { studentId: student.id });

      return student;
    } catch (error) {
      this.log('error', 'Student creation failed', { error, name: `${data.firstName} ${data.lastName}` });
      throw ErrorHandler.handle(error, 'Create student');
    }
  }

  /**
   * Complete student enrollment with all related data and atomic transactions
   */
  async enrollStudent(enrollmentData: StudentEnrollmentData): Promise<{
    student: Student;
    guardian: any;
    academicRecord: any;
    documents: any[];
  }> {
    const rollbackOperations: (() => Promise<void>)[] = [];

    try {
      this.log('info', 'Starting student enrollment', {
        studentName: `${enrollmentData.student.firstName} ${enrollmentData.student.lastName}`,
        rollNumber: enrollmentData.academic.rollNumber
      });

      // Step 1: Validate roll number availability before starting
      const rollNumberValidation = await this.academicService.validateRollNumber(
        enrollmentData.academic.rollNumber,
        enrollmentData.academic.classId,
        enrollmentData.academic.sectionId,
        enrollmentData.academic.academicYearId
      );

      if (!rollNumberValidation.isValid || !rollNumberValidation.isUnique) {
        throw new ServiceError(
          ErrorCode.ROLL_NUMBER_TAKEN,
          rollNumberValidation.message || 'Roll number is not available',
          null,
          'Student enrollment'
        );
      }

      // Step 2: Check class capacity
      const capacity = await this.academicService.getClassCapacity(
        enrollmentData.academic.classId,
        enrollmentData.academic.sectionId,
        enrollmentData.academic.academicYearId
      );

      if (capacity.isAtCapacity) {
        throw new ServiceError(
          ErrorCode.ENROLLMENT_LIMIT_EXCEEDED,
          'Class section is at full capacity',
          null,
          'Student enrollment',
          `This class section has reached its maximum capacity of ${capacity.maxCapacity} students.`
        );
      }

      // Step 3: Create student
      const student = await this.create(enrollmentData.student);
      rollbackOperations.push(() => this.delete(student.id));

      // Step 4: Create guardian
      const guardianData = {
        ...enrollmentData.guardian,
        studentId: student.id
      };
      const guardian = await this.guardianService.create(guardianData);
      rollbackOperations.push(() => this.guardianService.delete(guardian.id!));

      // Step 5: Create academic record
      const academicData = {
        ...enrollmentData.academic,
        studentId: student.id
      };
      const academicRecord = await this.academicService.createAcademicRecord(academicData);
      rollbackOperations.push(() => this.academicService.deleteAcademicRecord(student.id));

      // Step 6: Upload documents (if any)
      let documents: any[] = [];
      if (enrollmentData.documents && enrollmentData.documents.length > 0) {
        const documentRequests = enrollmentData.documents.map(doc => ({
          ...doc,
          studentId: student.id
        }));
        
        try {
          documents = await this.documentService.uploadDocuments(documentRequests);
          rollbackOperations.push(() => this.documentService.cleanupStudentDocuments(student.id));
        } catch (documentError) {
          this.log('warn', 'Document upload failed during enrollment', { documentError });
          // Continue with enrollment even if documents fail
          // Documents can be uploaded later
        }
      }

      // Step 7: Update student record with guardian and academic info
      await this.updateStudentWithEnrollmentData(student.id, guardian, academicRecord);

      this.log('info', 'Student enrollment completed successfully', {
        studentId: student.id,
        guardianId: guardian.id,
        academicRecordId: academicRecord.id,
        documentsCount: documents.length
      });

      return {
        student,
        guardian,
        academicRecord,
        documents
      };

    } catch (error) {
      this.log('error', 'Student enrollment failed, attempting rollback', { error });

      // Attempt rollback in reverse order
      for (const rollback of rollbackOperations.reverse()) {
        try {
          await rollback();
        } catch (rollbackError) {
          this.log('error', 'Rollback operation failed', { rollbackError });
        }
      }

      throw ErrorHandler.handle(error, 'Enroll student');
    }
  }

  /**
   * Find student by ID
   */
  async findById(id: string): Promise<Student | null> {
    try {
      const student = await this.executeQueryOptional(
        async () => this.client
          .from(DATABASE_TABLES.STUDENTS)
          .select('*')
          .eq('id', id)
          .eq('is_active', true)
          .single(),
        'Find student by ID'
      );

      return student;
    } catch (error) {
      throw ErrorHandler.handle(error, 'Find student by ID');
    }
  }

  /**
   * Find student with all relations
   */
  async findByIdWithRelations(id: string): Promise<StudentWithRelations | null> {
    try {
      const student = await this.findById(id);
      if (!student) {
        return null;
      }

      // Get guardians
      const guardians = await this.guardianService.findByStudentId(id);

      // Get academic record
      const academicRecord = await this.academicService.getAcademicRecordByStudentId(id);

      // Get documents
      const documents = await this.documentService.getStudentDocuments(id);

      return {
        ...student,
        guardians,
        academicRecord,
        documents: documents.documents
      };
    } catch (error) {
      throw ErrorHandler.handle(error, 'Find student with relations');
    }
  }

  /**
   * Find all students
   */
  async findAll(filters?: Record<string, any>): Promise<Student[]> {
    try {
      let query = this.client
        .from(DATABASE_TABLES.STUDENTS)
        .select('*')
        .eq('is_active', true);

      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          query = query.eq(key, value);
        });
      }

      const students = await this.executeQuery(
        async () => query.order('created_at', { ascending: false }),
        'Find all students'
      );

      return students;
    } catch (error) {
      throw ErrorHandler.handle(error, 'Find all students');
    }
  }

  /**
   * Update student
   */
  async update(id: string, data: Partial<EnhancedCreateStudentData>): Promise<Student> {
    try {
      this.log('info', 'Updating student', { studentId: id });

      // Check if student exists
      const exists = await this.exists(id);
      if (!exists) {
        throw new ServiceError(
          ErrorCode.NOT_FOUND,
          'Student not found',
          null,
          'Student update'
        );
      }

      // Validate email uniqueness if being updated
      if (data.email) {
        const isEmailUnique = await this.isEmailUnique(data.email, id);
        if (!isEmailUnique) {
          throw new ServiceError(
            ErrorCode.DUPLICATE_VALUE,
            'Email address already exists',
            null,
            'Student update',
            'This email address is already registered with another student.'
          );
        }
      }

      // Prepare update data
      const updateData = {
        first_name: data.firstName?.trim(),
        last_name: data.lastName?.trim(),
        date_of_birth: data.dateOfBirth,
        gender: data.gender,
        email: data.email?.trim() || null,
        phone_number: data.phoneNumber?.trim() || null,
        address: data.address?.trim() || null,
        blood_group: data.bloodGroup?.trim() || null,
        nationality: data.nationality?.trim() || null,
        religion: data.religion?.trim() || null,
        updated_at: new Date().toISOString()
      };

      // Remove undefined values
      Object.keys(updateData).forEach(key => {
        if (updateData[key as keyof typeof updateData] === undefined) {
          delete updateData[key as keyof typeof updateData];
        }
      });

      // Update student
      const student = await this.executeQuery(
        async () => this.client
          .from(DATABASE_TABLES.STUDENTS)
          .update(updateData)
          .eq('id', id)
          .select()
          .single(),
        'Update student'
      );

      this.log('info', 'Student updated successfully', { studentId: id });

      return student;
    } catch (error) {
      this.log('error', 'Student update failed', { error, studentId: id });
      throw ErrorHandler.handle(error, 'Update student');
    }
  }

  /**
   * Delete student (soft delete)
   */
  async delete(id: string): Promise<void> {
    try {
      await this.executeQuery(
        async () => this.client
          .from(DATABASE_TABLES.STUDENTS)
          .update({ 
            is_active: false, 
            updated_at: new Date().toISOString() 
          })
          .eq('id', id),
        'Delete student'
      );

      this.log('info', 'Student deleted', { studentId: id });
    } catch (error) {
      throw ErrorHandler.handle(error, 'Delete student');
    }
  }

  /**
   * Check if student exists
   */
  async exists(id: string): Promise<boolean> {
    try {
      const { data, error } = await this.client
        .from(DATABASE_TABLES.STUDENTS)
        .select('id')
        .eq('id', id)
        .eq('is_active', true)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw ErrorHandler.handle(error, 'Check student existence');
      }

      return data !== null;
    } catch (error) {
      if (error instanceof ServiceError && error.code === ErrorCode.NOT_FOUND) {
        return false;
      }
      throw error;
    }
  }

  // Private helper methods

  private validateCreateData(data: EnhancedCreateStudentData): void {
    this.validateRequired(data, ['firstName', 'lastName', 'dateOfBirth', 'gender']);

    // Validate date of birth
    const dob = new Date(data.dateOfBirth);
    if (isNaN(dob.getTime())) {
      throw new ServiceError(
        ErrorCode.INVALID_FORMAT,
        'Invalid date of birth format',
        null,
        'Student validation'
      );
    }

    // Validate age (must be between 3 and 25 years)
    const age = this.calculateAge(dob);
    if (age < 3 || age > 25) {
      throw new ServiceError(
        ErrorCode.VALIDATION_ERROR,
        'Student age must be between 3 and 25 years',
        null,
        'Student validation'
      );
    }

    // Validate email format if provided
    if (data.email && !this.isValidEmail(data.email)) {
      throw new ServiceError(
        ErrorCode.INVALID_FORMAT,
        'Invalid email format',
        null,
        'Student validation'
      );
    }

    // Validate gender
    const validGenders = ['male', 'female', 'other'];
    if (!validGenders.includes(data.gender.toLowerCase())) {
      throw new ServiceError(
        ErrorCode.VALIDATION_ERROR,
        'Invalid gender value',
        null,
        'Student validation'
      );
    }
  }

  private async isEmailUnique(email: string, excludeId?: string): Promise<boolean> {
    try {
      let query = this.client
        .from(DATABASE_TABLES.STUDENTS)
        .select('id')
        .eq('email', email)
        .eq('is_active', true);

      if (excludeId) {
        query = query.neq('id', excludeId);
      }

      const { data, error } = await query.single();

      if (error && error.code !== 'PGRST116') {
        throw ErrorHandler.handle(error, 'Check email uniqueness');
      }

      return data === null;
    } catch (error) {
      if (error instanceof ServiceError && error.code === ErrorCode.NOT_FOUND) {
        return true;
      }
      throw error;
    }
  }

  private async updateStudentWithEnrollmentData(
    studentId: string,
    guardian: any,
    academicRecord: any
  ): Promise<void> {
    try {
      await this.client
        .from(DATABASE_TABLES.STUDENTS)
        .update({
          guardian_name: guardian.name,
          guardian_relation_id: guardian.relationId,
          guardian_phone: guardian.phone,
          guardian_email: guardian.email,
          guardian_address: guardian.address,
          emergency_contact: guardian.emergencyContact,
          class_id: academicRecord.classId,
          section_id: academicRecord.sectionId,
          roll_number: academicRecord.rollNumber,
          academic_year_id: academicRecord.academicYearId,
          updated_at: new Date().toISOString()
        })
        .eq('id', studentId);
    } catch (error) {
      this.log('warn', 'Failed to update student with enrollment data', { error, studentId });
      // Don't throw error as this is not critical
    }
  }

  private calculateAge(dateOfBirth: Date): number {
    const today = new Date();
    let age = today.getFullYear() - dateOfBirth.getFullYear();
    const monthDiff = today.getMonth() - dateOfBirth.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < dateOfBirth.getDate())) {
      age--;
    }
    
    return age;
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
}
