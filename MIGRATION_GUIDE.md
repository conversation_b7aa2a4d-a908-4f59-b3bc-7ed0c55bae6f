# Service Architecture Migration Guide

## Overview

This guide documents the migration from the previous service architecture to the new modular and maintainable pattern with repository-based data access and consolidated business services.

## Architecture Changes

### Before (Legacy Architecture)
```
src/services/
├── core/
│   ├── baseService.ts
│   ├── databaseService.ts
│   └── errorHandler.ts
├── student/
│   ├── enhancedStudentService.ts
│   ├── guardianService.ts
│   └── academicService.ts
├── document/
│   ├── storageService.ts
│   └── documentService.ts
└── enrollment/
    ├── enrollmentService.ts
    └── validationService.ts
```

### After (New Architecture)
```
src/
├── constants/
│   └── database.ts (centralized schema)
├── repositories/
│   ├── baseRepository.ts
│   ├── studentRepository.ts
│   ├── guardianRepository.ts
│   ├── academicRecordRepository.ts
│   └── documentRepository.ts
├── services/
│   ├── studentManagementService.ts
│   ├── enrollmentManagementService.ts
│   ├── documentManagementService.ts
│   └── core/ (unchanged)
└── types/
    └── database.ts (enhanced)
```

## Key Improvements

### 1. Centralized Database Schema Management
- **File**: `src/constants/database.ts`
- **Purpose**: Single source of truth for all database-related constants
- **Benefits**: 
  - Type safety across the application
  - Easy schema evolution
  - Consistent naming conventions
  - Migration-friendly structure

### 2. Repository Pattern Implementation
- **Purpose**: Separate data access logic from business logic
- **Benefits**:
  - Testable data access layer
  - Consistent CRUD operations
  - Type-safe database interactions
  - Easy mocking for unit tests

### 3. Consolidated Business Services
- **Purpose**: One service per business domain instead of per database table
- **Benefits**:
  - Better business logic organization
  - Reduced service dependencies
  - Cleaner API surface
  - Easier maintenance

## Migration Steps

### Step 1: Update Imports

#### Before:
```typescript
import { EnhancedStudentService } from '../services/student/enhancedStudentService';
import { GuardianService } from '../services/student/guardianService';
import { AcademicService } from '../services/student/academicService';
```

#### After:
```typescript
import { StudentManagementService } from '../services/studentManagementService';
```

### Step 2: Update Service Factory Usage

#### Before:
```typescript
const studentService = await getService('studentService');
const guardianService = await getService('guardianService');
const academicService = await getService('academicService');
```

#### After:
```typescript
const studentManagementService = await getService('studentManagementService');
```

### Step 3: Update Method Calls

#### Before:
```typescript
// Creating a student required multiple service calls
const student = await studentService.createStudent(studentData);
const guardian = await guardianService.createGuardian(guardianData);
const academicRecord = await academicService.createAcademicRecord(academicData);
```

#### After:
```typescript
// Single service call for complete student creation
const completeStudent = await studentManagementService.createCompleteStudent({
  student: studentData,
  guardian: guardianData,
  academicRecord: academicData
});
```

### Step 4: Update Enrollment Process

#### Before:
```typescript
import { EnrollmentService } from '../services/enrollment/enrollmentService';
const enrollmentService = await getService('enrollmentService');
```

#### After:
```typescript
import { EnrollmentManagementService } from '../services/enrollmentManagementService';
const enrollmentService = await getService('enrollmentManagementService');
```

## Service Mapping

### Legacy to New Service Mapping

| Legacy Service | New Service | Notes |
|----------------|-------------|-------|
| `EnhancedStudentService` | `StudentManagementService` | Consolidated with guardian and academic operations |
| `GuardianService` | `StudentManagementService` | Methods moved to student management |
| `AcademicService` | `StudentManagementService` | Methods moved to student management |
| `EnrollmentService` | `EnrollmentManagementService` | Enhanced with better session management |
| `DocumentService` | `DocumentManagementService` | Enhanced with validation and statistics |
| `StorageService` | `DocumentManagementService` | Integrated into document management |
| `ValidationService` | `EnrollmentManagementService` | Integrated into enrollment process |

### Method Mapping Examples

#### Student Operations
```typescript
// Before
await studentService.createStudent(data);
await studentService.updateStudent(id, data);
await studentService.getStudentById(id);

// After
await studentManagementService.createCompleteStudent(data);
await studentManagementService.updateStudent(id, data);
await studentManagementService.getStudentById(id);
```

#### Guardian Operations
```typescript
// Before
await guardianService.createGuardian(data);
await guardianService.updateGuardian(id, data);

// After
await studentManagementService.addGuardian(studentId, data);
await studentManagementService.updateGuardian(id, data);
```

#### Enrollment Operations
```typescript
// Before
await enrollmentService.startEnrollment();
await enrollmentService.updateStep(sessionId, step, data);

// After
await enrollmentManagementService.startEnrollment();
await enrollmentManagementService.updateEnrollmentStep(sessionId, step, data);
```

## Component Updates

### Enhanced Enrollment Wizard

#### Before:
```typescript
import { EnrollmentService } from '../../services/enrollment/enrollmentService';
const service = await getService('enrollmentService');
```

#### After:
```typescript
import { EnrollmentManagementService } from '../../services/enrollmentManagementService';
const service = await getService('enrollmentManagementService');
```

## Database Schema Constants

### Usage Examples

#### Before:
```typescript
// Hard-coded table names
const { data } = await client.from('students').select('*');
```

#### After:
```typescript
import { DATABASE_TABLES, DATABASE_COLUMNS } from '../constants/database';

// Type-safe table and column names
const { data } = await client
  .from(DATABASE_TABLES.STUDENTS)
  .select('*')
  .eq(DATABASE_COLUMNS.STUDENTS.IS_ACTIVE, true);
```

## Type Safety Improvements

### Entity Types
```typescript
import {
  StudentEntity,
  GuardianEntity,
  AcademicRecordEntity,
  DocumentEntity
} from '../constants/database';
```

### Repository Interfaces
```typescript
import { IStudentRepository } from '../repositories/studentRepository';
import { IGuardianRepository } from '../repositories/guardianRepository';
```

## Testing Considerations

### Repository Mocking
```typescript
// Easy to mock repositories for unit testing
const mockStudentRepository: IStudentRepository = {
  findById: jest.fn(),
  create: jest.fn(),
  // ... other methods
};
```

### Service Testing
```typescript
// Services can be tested with mocked repositories
const studentService = new StudentManagementService(mockClient);
// Inject mocked repositories if needed
```

## Backward Compatibility

### Legacy Service Support
- The `ValidationService` is still available for backward compatibility
- Existing components can be migrated gradually
- Service factory supports both old and new service patterns

### Migration Strategy
1. **Phase 1**: Update service factory and core infrastructure
2. **Phase 2**: Migrate enrollment wizard to new services
3. **Phase 3**: Update other components gradually
4. **Phase 4**: Remove legacy services once migration is complete

## Benefits Realized

### Code Quality
- ✅ Better separation of concerns
- ✅ Improved type safety
- ✅ Consistent error handling
- ✅ Easier unit testing

### Maintainability
- ✅ Single source of truth for database schema
- ✅ Reduced code duplication
- ✅ Clear business logic organization
- ✅ Easier to add new features

### Performance
- ✅ Reduced service dependencies
- ✅ Better caching opportunities
- ✅ Optimized database queries

### Developer Experience
- ✅ Better IntelliSense support
- ✅ Compile-time error checking
- ✅ Clear API documentation
- ✅ Easier debugging

## Next Steps

1. **Complete Migration**: Update remaining components to use new services
2. **Add Unit Tests**: Implement comprehensive test coverage for repositories and services
3. **Performance Optimization**: Add caching and query optimization
4. **Documentation**: Complete API documentation for all services
5. **Monitoring**: Add service health monitoring and metrics

## Troubleshooting

### Common Issues

#### Import Errors
```typescript
// Error: Cannot find module
import { EnhancedStudentService } from '../services/student/enhancedStudentService';

// Solution: Update to new service
import { StudentManagementService } from '../services/studentManagementService';
```

#### Service Not Found
```typescript
// Error: Service 'studentService' not found
const service = await getService('studentService');

// Solution: Use new service name
const service = await getService('studentManagementService');
```

#### Type Errors
```typescript
// Error: Property does not exist
const student = await service.createStudent(data);

// Solution: Use new method signature
const completeStudent = await service.createCompleteStudent({
  student: data.student,
  guardian: data.guardian,
  academicRecord: data.academic
});
```

## Support

For questions or issues during migration:
1. Check this migration guide
2. Review the new service interfaces
3. Look at the enhanced enrollment wizard implementation as an example
4. Consult the repository pattern documentation
