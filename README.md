# EduPro - School Management System

A comprehensive school management system built with Next.js 13+, TypeScript, Supabase, and Tailwind CSS. This application provides a complete solution for managing students, classes, academic years, and school operations with a modern, responsive interface.

## 🚀 Features

- **Student Management**: Complete CRUD operations for student records
- **Master Data Management**: Classes, sections, academic years, guardian relations
- **Authentication**: Secure user authentication with Supabase Auth
- **Responsive Design**: Modern UI with Tailwind CSS
- **Type Safety**: Full TypeScript implementation
- **Centralized Database Configuration**: Easy table name management
- **Service Layer Architecture**: Clean separation of concerns
- **Real-time Validation**: Live roll number checking and form validation

## 🏗️ Architecture Overview

```
├── Frontend (Next.js 13+ App Router)
├── Service Layer (TypeScript Classes)
├── API Routes (Next.js API Routes)
├── Database (Supabase PostgreSQL)
└── Authentication (Supabase Auth)
```

## 📦 Tech Stack

- **Frontend**: Next.js 13+, TypeScript, Tailwind CSS
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **Styling**: Tailwind CSS
- **State Management**: React Hooks + Context
- **Form Handling**: React Hook Form (planned)
- **Validation**: Real-time validation with Supabase

## 🛠️ Installation & Setup

### Prerequisites

- Node.js 18+ installed
- Supabase account and project

### 1. Clone and Install

```bash
git clone <repository-url>
cd edupro
npm install
```

### 2. Environment Setup

Create a `.env` file in the root directory:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
SUPABASE_PROJECT_ID=your_project_id
```

### 3. Database Setup

The application expects the following tables in your Supabase database:

```sql
-- Core Tables (create these in your Supabase SQL editor)
CREATE TABLE profiles (
  id UUID REFERENCES auth.users PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  full_name TEXT,
  role TEXT CHECK (role IN ('student', 'teacher', 'admin', 'parent')) DEFAULT 'student',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE classes (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE sections (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE academic_years (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  year TEXT NOT NULL,
  start_date DATE NOT NULL,
  end_date DATE NOT NULL,
  is_active BOOLEAN DEFAULT true,
  is_current BOOLEAN DEFAULT false,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE guardian_relations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE students (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  first_name TEXT NOT NULL,
  last_name TEXT NOT NULL,
  date_of_birth DATE NOT NULL,
  gender TEXT CHECK (gender IN ('male', 'female', 'other')) NOT NULL,
  email TEXT,
  phone_number TEXT,
  address TEXT,
  guardian_name TEXT NOT NULL,
  guardian_relation_id UUID REFERENCES guardian_relations(id),
  guardian_phone TEXT NOT NULL,
  guardian_email TEXT,
  guardian_address TEXT,
  emergency_contact TEXT,
  class_id UUID REFERENCES classes(id),
  section_id UUID REFERENCES sections(id),
  roll_number TEXT NOT NULL,
  previous_school TEXT,
  academic_year_id UUID REFERENCES academic_years(id),
  profile_id UUID REFERENCES profiles(id),
  birth_certificate_url TEXT,
  previous_records_url TEXT,
  medical_records_url TEXT,
  photograph_url TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(roll_number, class_id, section_id, academic_year_id)
);
```

### 4. Start Development Server

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to view the application.

## 🗄️ Database Configuration System

### Overview

EduPro uses a centralized database configuration system that allows you to change table names across the entire application by updating a single configuration file.

### Configuration File: `src/config/database.ts`

```typescript
export const DATABASE_TABLES = {
  // Master Tables
  CLASSES: 'classes',
  SECTIONS: 'sections', 
  ACADEMIC_YEARS: 'academic_years',
  GUARDIAN_RELATIONS: 'guardian_relations',
  
  // Student Tables
  STUDENTS: 'students',
  
  // Future Tables (ready for expansion)
  TEACHERS: 'teachers',
  SUBJECTS: 'subjects',
  // ... more tables
} as const;
```

### How to Change Table Names

#### Step 1: Update Configuration

Edit `src/config/database.ts`:

```typescript
export const DATABASE_TABLES = {
  CLASSES: 'school_classes',        // Changed from 'classes'
  SECTIONS: 'class_sections',       // Changed from 'sections'
  STUDENTS: 'enrolled_students',    // Changed from 'students'
  // ... other tables
} as const;
```

#### Step 2: Restart Application

```bash
npm run dev
```

That's it! The entire application now uses your new table names.

### Usage in Code

#### ✅ Correct Usage (Recommended)

```typescript
import { DATABASE_TABLES } from '../config/database';

// In service classes
const { data } = await supabase
  .from(DATABASE_TABLES.STUDENTS)
  .select('*');

// With destructuring
import { STUDENTS, CLASSES } from '../config/database';
const studentsData = await supabase.from(STUDENTS).select('*');
```

#### ❌ Incorrect Usage (Avoid)

```typescript
// Don't hardcode table names
const { data } = await supabase
  .from('students')  // ❌ Hardcoded
  .select('*');
```

## 🔧 Service Layer Architecture

### Master Data Service: `src/services/masterDataService.ts`

Handles all master data operations (classes, sections, academic years, guardian relations).

```typescript
import { MasterDataService } from '../services/masterDataService';

// Get all classes
const classes = await MasterDataService.getClasses();

// Get current academic year
const currentYear = await MasterDataService.getCurrentAcademicYear();

// Get all master data at once
const masterData = await MasterDataService.getAllMasterData();
```

### Student Service: `src/services/studentService.ts`

Handles all student-related operations.

```typescript
import { StudentService } from '../services/studentService';

// Create a new student
const newStudent = await StudentService.createStudent({
  firstName: 'John',
  lastName: 'Doe',
  dateOfBirth: '2010-05-15',
  gender: 'male',
  guardianName: 'Jane Doe',
  guardianRelationId: 'parent-id',
  guardianPhone: '+1234567890',
  classId: 'class-id',
  sectionId: 'section-id',
  rollNumber: '2024001',
  academicYearId: 'year-id'
});

// Get all students with relations
const students = await StudentService.getAllStudents();

// Search students
const searchResults = await StudentService.searchStudents('John');

// Update student
const updated = await StudentService.updateStudent('student-id', {
  firstName: 'Johnny'
});

// Delete student (soft delete)
await StudentService.deleteStudent('student-id');
```

## 🎣 React Hooks

### useMasterData Hook: `src/hooks/useMasterData.ts`

```typescript
import { useMasterData } from '../hooks/useMasterData';

function MyComponent() {
  const { 
    data: masterData, 
    loading, 
    error,
    classes,
    sections,
    academicYears,
    guardianRelations,
    currentAcademicYear
  } = useMasterData();

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div>
      <select>
        {classes.map(cls => (
          <option key={cls.id} value={cls.id}>{cls.name}</option>
        ))}
      </select>
    </div>
  );
}
```

### useStudents Hook: `src/hooks/useStudents.ts`

```typescript
import { useStudents } from '../hooks/useStudents';

function StudentManagement() {
  const { createStudent, loading, error } = useStudents();

  const handleCreateStudent = async (studentData) => {
    try {
      await createStudent(studentData);
      alert('Student created successfully!');
    } catch (err) {
      console.error('Failed to create student:', err);
    }
  };

  return (
    <form onSubmit={handleCreateStudent}>
      {/* Form fields */}
    </form>
  );
}
```

## 🌐 API Routes

### Master Data API: `/api/master-data`

```typescript
// Get all master data
fetch('/api/master-data')

// Get specific data type
fetch('/api/master-data?type=classes')
fetch('/api/master-data?type=sections')
fetch('/api/master-data?type=academic-years')
fetch('/api/master-data?type=guardian-relations')
fetch('/api/master-data?type=current-academic-year')
```

### Students API: `/api/students`

```typescript
// Create student
fetch('/api/students', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(studentData)
})

// Get all students
fetch('/api/students')

// Search students
fetch('/api/students?search=john')

// Get students by class and section
fetch('/api/students?classId=xxx&sectionId=yyy')
```

### Roll Number Validation: `/api/students/check-roll-number`

```typescript
// Check if roll number is available
fetch('/api/students/check-roll-number', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    rollNumber: '2024001',
    classId: 'class-id',
    sectionId: 'section-id',
    academicYearId: 'year-id'
  })
})
```

## 🎨 UI Components

### Add Student Wizard: `src/components/student-management/add-student-wizard.tsx`

A comprehensive multi-step form for student enrollment with:

- Real-time roll number validation
- Dynamic dropdowns from Supabase master tables
- Form validation and error handling
- File upload support (planned)

```typescript
import AddStudentWizard from '../components/student-management/add-student-wizard';

function StudentManagement() {
  const [showWizard, setShowWizard] = useState(false);

  return (
    <div>
      <button onClick={() => setShowWizard(true)}>
        Add New Student
      </button>
      
      {showWizard && (
        <AddStudentWizard
          onClose={() => setShowWizard(false)}
          onSuccess={(message) => alert(message)}
          onError={(error) => alert(error)}
        />
      )}
    </div>
  );
}
```

## 🔒 Authentication

### Setup Authentication Provider

```typescript
// src/app/layout.tsx
import { AuthProvider } from '../components/auth/auth-provider';

export default function RootLayout({ children }) {
  return (
    <html>
      <body>
        <AuthProvider>
          {children}
        </AuthProvider>
      </body>
    </html>
  );
}
```

### Use Authentication in Components

```typescript
import { useAuth } from '../components/auth/auth-provider';

function Dashboard() {
  const { user, loading, signOut } = useAuth();

  if (loading) return <div>Loading...</div>;
  if (!user) return <div>Please sign in</div>;

  return (
    <div>
      <h1>Welcome, {user.email}</h1>
      <button onClick={signOut}>Sign Out</button>
    </div>
  );
}
```

## 🔧 Development Workflow

### Adding New Tables

1. **Update Database Configuration**:
```typescript
// src/config/database.ts
export const DATABASE_TABLES = {
  // ...existing tables
  TEACHERS: 'teachers',        // Add new table
  SUBJECTS: 'subjects',        // Add new table
} as const;
```

2. **Update Database Types**:
```typescript
// src/types/database.ts
export interface Database {
  public: {
    Tables: {
      // ...existing tables
      teachers: {
        Row: {
          id: string;
          name: string;
          // ...other fields
        };
        Insert: {
          // ...insert fields
        };
        Update: {
          // ...update fields
        };
      };
    };
  };
}
```

3. **Create Service Class**:
```typescript
// src/services/teacherService.ts
import { DATABASE_TABLES } from '../config/database';

export class TeacherService {
  static async getAllTeachers() {
    return await supabase
      .from(DATABASE_TABLES.TEACHERS)
      .select('*');
  }
}
```

4. **Create React Hook**:
```typescript
// src/hooks/useTeachers.ts
export const useTeachers = () => {
  // Implementation
};
```

5. **Add API Route**:
```typescript
// src/app/api/teachers/route.ts
export async function GET() {
  // Implementation
}
```

### Testing Changes

```bash
# Build and check for errors
npm run build

# Run development server
npm run dev

# Test database connection
node -e "
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL, process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY);
supabase.from('classes').select('*').limit(1).then(console.log);
"
```

## 🐛 Troubleshooting

### Common Issues

1. **Environment Variables**: Ensure all Supabase environment variables are set correctly
2. **Database Permissions**: Check RLS policies in Supabase
3. **Table Names**: Verify table names match your configuration
4. **TypeScript Errors**: Run `npm run build` to check for type issues

### Database Connection Test

```typescript
// test-connection.js
const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
);

async function testConnection() {
  try {
    const { data, error } = await supabase.from('classes').select('*').limit(1);
    if (error) throw error;
    console.log('✅ Database connection successful:', data);
  } catch (error) {
    console.error('❌ Database connection failed:', error);
  }
}

testConnection();
```

## 📱 Deployment

### Environment Variables for Production

```env
NEXT_PUBLIC_SUPABASE_URL=your_production_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_production_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_production_service_role_key
```

### Build and Deploy

```bash
# Build for production
npm run build

# Start production server
npm start
```

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch: `git checkout -b feature/amazing-feature`
3. Update the database configuration if adding new tables
4. Follow the service layer pattern for new features
5. Add proper TypeScript types
6. Test your changes: `npm run build`
7. Commit your changes: `git commit -m 'Add amazing feature'`
8. Push to the branch: `git push origin feature/amazing-feature`
9. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Check the troubleshooting section above
- Review the database configuration documentation
- Ensure all environment variables are properly set
- Verify Supabase table structure matches the expected schema

---

**Built with ❤️ using Next.js, TypeScript, and Supabase**
