// src/app/api/students/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { MasterDataService } from '../../../services/masterDataService';
import { CreateStudentData, StudentService } from '../../../services/studentService';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search');
    const classId = searchParams.get('classId');
    const sectionId = searchParams.get('sectionId');
    const academicYearId = searchParams.get('academicYearId');

    let students;

    if (search) {
      students = await StudentService.searchStudents(search);
    } else if (classId && sectionId) {
      students = await StudentService.getStudentsByClassAndSection(
        classId, 
        sectionId, 
        academicYearId || undefined
      );
    } else {
      students = await StudentService.getAllStudents();
    }

    return NextResponse.json({ 
      success: true, 
      data: students 
    });
  } catch (error) {
    console.error('Error in GET /api/students:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to fetch students' 
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required fields
    const requiredFields = [
      'firstName', 'lastName', 'dateOfBirth', 'gender', 'guardianName',
      'guardianRelationId', 'guardianPhone', 'classId', 'sectionId',
      'rollNumber', 'academicYearId'
    ];

    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          { success: false, error: `${field} is required` },
          { status: 400 }
        );
      }
    }

    // Check if roll number already exists
    const rollNumberExists = await MasterDataService.isRollNumberExists(
      body.rollNumber,
      body.classId,
      body.sectionId,
      body.academicYearId
    );

    if (rollNumberExists) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Roll number already exists for this class, section, and academic year' 
        },
        { status: 409 }
      );
    }

    const studentData: CreateStudentData = {
      firstName: body.firstName,
      lastName: body.lastName,
      dateOfBirth: body.dateOfBirth,
      gender: body.gender,
      email: body.email,
      phoneNumber: body.phoneNumber,
      address: body.address,
      guardianName: body.guardianName,
      guardianRelationId: body.guardianRelationId,
      guardianPhone: body.guardianPhone,
      guardianEmail: body.guardianEmail,
      guardianAddress: body.guardianAddress,
      emergencyContact: body.emergencyContact,
      classId: body.classId,
      sectionId: body.sectionId,
      rollNumber: body.rollNumber,
      previousSchool: body.previousSchool,
      academicYearId: body.academicYearId,
      birthCertificateUrl: body.birthCertificateUrl,
      previousRecordsUrl: body.previousRecordsUrl,
      medicalRecordsUrl: body.medicalRecordsUrl,
      photographUrl: body.photographUrl
    };

    const student = await StudentService.createStudent(studentData);

    return NextResponse.json(
      { success: true, data: student },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error in POST /api/students:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to create student' 
      },
      { status: 500 }
    );
  }
}
