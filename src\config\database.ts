// src/config/database.ts
/**
 * Database Table Configuration
 * 
 * This file contains all table names used throughout the application.
 * To change table names, simply update the values in this file and
 * the entire application will use the new names automatically.
 */

export const DATABASE_TABLES = {
  // Master Tables
  CLASSES: 'classes',
  SECTIONS: 'sections', 
  ACADEMIC_YEARS: 'academic_years',
  GUARDIAN_RELATIONS: 'guardian_relations',
  
  // Student Tables
  STUDENTS: 'students',
  
  // Future Tables (ready for expansion)
  TEACHERS: 'teachers',
  SUBJECTS: 'subjects',
  ENROLLMENTS: 'enrollments',
  ATTENDANCE: 'attendance',
  GRADES: 'grades',
  FEES: 'fees',
  EXAMS: 'exams',
  ANNOUNCEMENTS: 'announcements',
  LIBRARY_BOOKS: 'library_books',
  LIBRARY_ISSUES: 'library_issues',
} as const;

// Type for table names (for TypeScript safety)
export type DatabaseTableName = typeof DATABASE_TABLES[keyof typeof DATABASE_TABLES];

// Helper function to get table name (with validation)
export const getTableName = (tableKey: keyof typeof DATABASE_TABLES): string => {
  const tableName = DATABASE_TABLES[tableKey];
  if (!tableName) {
    throw new Error(`Table name not found for key: ${tableKey}`);
  }
  return tableName;
};

// Export individual table names for convenience
export const {
  CLASSES,
  SECTIONS,
  ACADEMIC_YEARS,
  GUARDIAN_RELATIONS,
  STUDENTS,
  TEACHERS,
  SUBJECTS,
  ENROLLMENTS,
  ATTENDANCE,
  GRADES,
  FEES,
  EXAMS,
  ANNOUNCEMENTS,
  LIBRARY_BOOKS,
  LIBRARY_ISSUES,
} = DATABASE_TABLES;

// Database schema configuration
export const DATABASE_CONFIG = {
  // Schema name (if using multiple schemas)
  SCHEMA: 'public',
  
  // Default pagination settings
  DEFAULT_PAGE_SIZE: 50,
  MAX_PAGE_SIZE: 100,
  
  // File upload settings
  STORAGE_BUCKET: 'student-documents',
  MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB
  
  // Database connection settings
  CONNECTION_TIMEOUT: 10000, // 10 seconds
  QUERY_TIMEOUT: 30000, // 30 seconds
} as const;

export default DATABASE_TABLES;
