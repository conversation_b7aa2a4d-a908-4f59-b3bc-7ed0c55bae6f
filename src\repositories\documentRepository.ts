// src/repositories/documentRepository.ts
import { SupabaseClient } from '@supabase/supabase-js';
import {
  DATABASE_COLUMNS,
  DATABASE_CONFIG,
  DATABASE_TABLES,
  DocumentEntity,
  DocumentInsert,
  DocumentUpdate
} from '../constants/database';
import { <PERSON>rror<PERSON>ode, ErrorHandler, ServiceError } from '../services/core/errorHandler';
import { Database } from '../types/database';
import { BaseRepository, PaginatedResult, PaginationOptions } from './baseRepository';

/**
 * Document-specific repository interface
 */
export interface IDocumentRepository {
  findByStudentId(studentId: string): Promise<DocumentEntity[]>;
  findByType(studentId: string, type: string): Promise<DocumentEntity[]>;
  findByStatus(status: string, pagination?: PaginationOptions): Promise<PaginatedResult<DocumentEntity>>;
  updateStatus(id: string, status: string, notes?: string): Promise<DocumentEntity>;
  findRequiredDocuments(studentId: string): Promise<DocumentEntity[]>;
  findMissingRequiredDocuments(studentId: string): Promise<string[]>;
  getDocumentStats(studentId: string): Promise<{ total: number; approved: number; pending: number; rejected: number }>;
  cleanupOrphanedDocuments(olderThanDays: number): Promise<string[]>;
}

/**
 * Document repository implementation
 * Handles all database operations related to documents
 */
export class DocumentRepository extends BaseRepository<DocumentEntity, DocumentInsert, DocumentUpdate> implements IDocumentRepository {
  
  constructor(client: SupabaseClient<Database>) {
    super(client, DATABASE_TABLES.DOCUMENTS);
  }

  /**
   * Find all documents for a specific student
   * @param studentId - Student ID
   * @returns Array of document entities
   */
  async findByStudentId(studentId: string): Promise<DocumentEntity[]> {
    try {
      this.log('info', 'Finding documents by student ID', { studentId });

      const { data, error } = await this.client
        .from(this.tableName)
        .select('*')
        .eq(DATABASE_COLUMNS.DOCUMENTS.STUDENT_ID, studentId)
        .eq(DATABASE_COLUMNS.COMMON.IS_ACTIVE, true)
        .order(DATABASE_COLUMNS.DOCUMENTS.UPLOADED_AT, { ascending: false });

      if (error) {
        throw ErrorHandler.handle(error, 'Find documents by student ID');
      }

      this.log('info', 'Documents found by student ID', { studentId, count: data?.length || 0 });
      return (data || []) as DocumentEntity[];
    } catch (error) {
      this.log('error', 'Error finding documents by student ID', { error, studentId });
      throw ErrorHandler.handle(error, 'Find documents by student ID');
    }
  }

  /**
   * Find documents by type for a specific student
   * @param studentId - Student ID
   * @param type - Document type
   * @returns Array of document entities
   */
  async findByType(studentId: string, type: string): Promise<DocumentEntity[]> {
    try {
      this.log('info', 'Finding documents by type', { studentId, type });

      const { data, error } = await this.client
        .from(this.tableName as any)
        .select('*')
        .eq(DATABASE_COLUMNS.DOCUMENTS.STUDENT_ID, studentId)
        .eq(DATABASE_COLUMNS.DOCUMENTS.TYPE, type)
        .eq(DATABASE_COLUMNS.COMMON.IS_ACTIVE, true)
        .order(DATABASE_COLUMNS.DOCUMENTS.UPLOADED_AT, { ascending: false });

      if (error) {
        throw ErrorHandler.handle(error, 'Find documents by type');
      }

      this.log('info', 'Documents found by type', { studentId, type, count: data?.length || 0 });
      return (data || []) as DocumentEntity[];
    } catch (error) {
      this.log('error', 'Error finding documents by type', { error, studentId, type });
      throw ErrorHandler.handle(error, 'Find documents by type');
    }
  }

  /**
   * Find documents by status with pagination
   * @param status - Document status
   * @param pagination - Pagination options
   * @returns Paginated list of documents
   */
  async findByStatus(
    status: string,
    pagination?: PaginationOptions
  ): Promise<PaginatedResult<DocumentEntity>> {
    try {
      this.log('info', 'Finding documents by status', { status });

      const filters = {
        [DATABASE_COLUMNS.DOCUMENTS.STATUS]: status
      };

      const result = await this.findAll(filters, {
        ...pagination,
        sortBy: DATABASE_COLUMNS.DOCUMENTS.UPLOADED_AT,
        sortOrder: 'desc'
      });

      this.log('info', 'Documents found by status', { status, count: result.data.length });
      return result;
    } catch (error) {
      this.log('error', 'Error finding documents by status', { error, status });
      throw ErrorHandler.handle(error, 'Find documents by status');
    }
  }

  /**
   * Update document status
   * @param id - Document ID
   * @param status - New status
   * @param notes - Optional notes
   * @returns Updated document entity
   */
  async updateStatus(id: string, status: string, notes?: string): Promise<DocumentEntity> {
    try {
      this.log('info', 'Updating document status', { documentId: id, status, notes });

      // Validate status
      const validStatuses = Object.values(DATABASE_CONFIG.DOCUMENT_STATUS);
      if (!validStatuses.includes(status as any)) {
        throw new ServiceError(
          ErrorCode.VALIDATION_ERROR,
          `Invalid document status: ${status}`,
          null,
          'Update document status'
        );
      }

      const updateData: DocumentUpdate = {
        status: status as any,
        notes,
        updated_at: new Date().toISOString()
      };

      const document = await super.update(id, updateData);
      this.log('info', 'Document status updated successfully', { documentId: id, status });
      return document;
    } catch (error) {
      this.log('error', 'Error updating document status', { error, documentId: id, status });
      throw ErrorHandler.handle(error, 'Update document status');
    }
  }

  /**
   * Find required documents for a student
   * @param studentId - Student ID
   * @returns Array of required document entities
   */
  async findRequiredDocuments(studentId: string): Promise<DocumentEntity[]> {
    try {
      this.log('info', 'Finding required documents', { studentId });

      const { data, error } = await this.client
        .from(this.tableName as any)
        .select('*')
        .eq(DATABASE_COLUMNS.DOCUMENTS.STUDENT_ID, studentId)
        .eq(DATABASE_COLUMNS.DOCUMENTS.IS_REQUIRED, true)
        .eq(DATABASE_COLUMNS.COMMON.IS_ACTIVE, true)
        .order(DATABASE_COLUMNS.DOCUMENTS.UPLOADED_AT, { ascending: false });

      if (error) {
        throw ErrorHandler.handle(error, 'Find required documents');
      }

      this.log('info', 'Required documents found', { studentId, count: data?.length || 0 });
      return (data || []) as DocumentEntity[];
    } catch (error) {
      this.log('error', 'Error finding required documents', { error, studentId });
      throw ErrorHandler.handle(error, 'Find required documents');
    }
  }

  /**
   * Find missing required document types for a student
   * @param studentId - Student ID
   * @returns Array of missing document types
   */
  async findMissingRequiredDocuments(studentId: string): Promise<string[]> {
    try {
      this.log('info', 'Finding missing required documents', { studentId });

      // Get all uploaded document types for the student
      const { data: uploadedDocs, error } = await this.client
        .from(this.tableName as any)
        .select(DATABASE_COLUMNS.DOCUMENTS.TYPE)
        .eq(DATABASE_COLUMNS.DOCUMENTS.STUDENT_ID, studentId)
        .eq(DATABASE_COLUMNS.COMMON.IS_ACTIVE, true);

      if (error) {
        throw ErrorHandler.handle(error, 'Find uploaded document types');
      }

      const uploadedTypes = (uploadedDocs || []).map(doc => doc.type);
      const requiredTypes = DATABASE_CONFIG.REQUIRED_DOCUMENTS;
      const missingTypes = requiredTypes.filter(type => !uploadedTypes.includes(type));

      this.log('info', 'Missing required documents found', { 
        studentId, 
        missing: missingTypes.length,
        types: missingTypes 
      });

      return missingTypes;
    } catch (error) {
      this.log('error', 'Error finding missing required documents', { error, studentId });
      throw ErrorHandler.handle(error, 'Find missing required documents');
    }
  }

  /**
   * Get document statistics for a student
   * @param studentId - Student ID
   * @returns Document statistics
   */
  async getDocumentStats(studentId: string): Promise<{
    total: number;
    approved: number;
    pending: number;
    rejected: number;
  }> {
    try {
      this.log('info', 'Getting document statistics', { studentId });

      const documents = await this.findByStudentId(studentId);
      
      const stats = {
        total: documents.length,
        approved: documents.filter(doc => doc.status === DATABASE_CONFIG.DOCUMENT_STATUS.APPROVED).length,
        pending: documents.filter(doc => doc.status === DATABASE_CONFIG.DOCUMENT_STATUS.PENDING).length,
        rejected: documents.filter(doc => doc.status === DATABASE_CONFIG.DOCUMENT_STATUS.REJECTED).length
      };

      this.log('info', 'Document statistics calculated', { studentId, stats });
      return stats;
    } catch (error) {
      this.log('error', 'Error getting document statistics', { error, studentId });
      throw ErrorHandler.handle(error, 'Get document statistics');
    }
  }

  /**
   * Clean up orphaned documents (documents not referenced by active students)
   * @param olderThanDays - Delete documents older than this many days
   * @returns Array of deleted document IDs
   */
  async cleanupOrphanedDocuments(olderThanDays: number = 30): Promise<string[]> {
    try {
      this.log('info', 'Cleaning up orphaned documents', { olderThanDays });

      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

      // Find documents that are older than cutoff and belong to inactive students
      const { data: orphanedDocs, error } = await this.client
        .from(this.tableName as any)
        .select(`
          ${DATABASE_COLUMNS.DOCUMENTS.ID},
          ${DATABASE_COLUMNS.DOCUMENTS.STUDENT_ID},
          student:${DATABASE_TABLES.STUDENTS}(${DATABASE_COLUMNS.COMMON.IS_ACTIVE})
        `)
        .eq(DATABASE_COLUMNS.COMMON.IS_ACTIVE, true)
        .lt(DATABASE_COLUMNS.COMMON.CREATED_AT, cutoffDate.toISOString());

      if (error) {
        throw ErrorHandler.handle(error, 'Find orphaned documents');
      }

      // Filter documents where student is inactive or doesn't exist
      const documentsToDelete = (orphanedDocs || [])
        .filter((doc: any) => !doc.student || !doc.student.is_active)
        .map((doc: any) => doc.id);

      if (documentsToDelete.length > 0) {
        // Soft delete the orphaned documents
        const { error: deleteError } = await this.client
          .from(this.tableName as any)
          .update({
            [DATABASE_COLUMNS.COMMON.IS_ACTIVE]: false,
            [DATABASE_COLUMNS.COMMON.UPDATED_AT]: new Date().toISOString()
          })
          .in(DATABASE_COLUMNS.COMMON.ID, documentsToDelete);

        if (deleteError) {
          throw ErrorHandler.handle(deleteError, 'Delete orphaned documents');
        }
      }

      this.log('info', 'Orphaned documents cleaned up', { 
        olderThanDays, 
        deletedCount: documentsToDelete.length 
      });

      return documentsToDelete;
    } catch (error) {
      this.log('error', 'Error cleaning up orphaned documents', { error, olderThanDays });
      throw ErrorHandler.handle(error, 'Cleanup orphaned documents');
    }
  }

  /**
   * Override create method to add document-specific validation
   */
  async create(data: DocumentInsert): Promise<DocumentEntity> {
    try {
      this.log('info', 'Creating document', { 
        studentId: data.student_id, 
        type: data.type,
        fileName: data.file_name 
      });

      // Validate required fields
      this.validateEntity(data as any, [
        'student_id',
        'type',
        'file_name',
        'original_name',
        'file_path',
        'file_url',
        'file_size',
        'mime_type'
      ] as any);

      // Validate file size
      if (data.file_size > DATABASE_CONFIG.MAX_FILE_SIZE) {
        throw new ServiceError(
          ErrorCode.FILE_TOO_LARGE,
          `File size ${data.file_size} exceeds maximum allowed size ${DATABASE_CONFIG.MAX_FILE_SIZE}`,
          null,
          'Create document'
        );
      }

      // Validate file type
      if (!DATABASE_CONFIG.ALLOWED_FILE_TYPES.includes(data.mime_type)) {
        throw new ServiceError(
          ErrorCode.INVALID_FILE_TYPE,
          `File type ${data.mime_type} is not allowed`,
          null,
          'Create document'
        );
      }

      const document = await super.create(data);
      this.log('info', 'Document created successfully', { 
        documentId: document.id, 
        studentId: data.student_id,
        type: data.type 
      });
      return document;
    } catch (error) {
      this.log('error', 'Error creating document', { error, data });
      throw ErrorHandler.handle(error, 'Create document');
    }
  }
}
