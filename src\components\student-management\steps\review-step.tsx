// src/components/student-management/steps/review-step.tsx
'use client';

import React from 'react';

interface ReviewStepProps {
  data: any;
  onComplete: () => void;
  onBack?: () => void;
  submitting: boolean;
}

const ReviewStep: React.FC<ReviewStepProps> = ({
  data,
  onComplete,
  onBack,
  submitting
}) => {
  return (
    <div>
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Review & Submit</h2>
        <p className="text-gray-600">Review all information before submitting the enrollment</p>
      </div>

      <div className="space-y-6">
        {/* Personal Information */}
        {data.student && (
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="font-medium text-gray-900 mb-3">Personal Information</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Name:</span>
                <span className="ml-2 font-medium">{data.student.firstName} {data.student.lastName}</span>
              </div>
              <div>
                <span className="text-gray-600">Date of Birth:</span>
                <span className="ml-2 font-medium">{data.student.dateOfBirth}</span>
              </div>
              <div>
                <span className="text-gray-600">Gender:</span>
                <span className="ml-2 font-medium capitalize">{data.student.gender}</span>
              </div>
              {data.student.email && (
                <div>
                  <span className="text-gray-600">Email:</span>
                  <span className="ml-2 font-medium">{data.student.email}</span>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Guardian Information */}
        {data.guardian && (
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="font-medium text-gray-900 mb-3">Guardian Information</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Name:</span>
                <span className="ml-2 font-medium">{data.guardian.name}</span>
              </div>
              <div>
                <span className="text-gray-600">Phone:</span>
                <span className="ml-2 font-medium">{data.guardian.phone}</span>
              </div>
            </div>
          </div>
        )}

        {/* Academic Information */}
        {data.academic && (
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="font-medium text-gray-900 mb-3">Academic Information</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Roll Number:</span>
                <span className="ml-2 font-medium">{data.academic.rollNumber}</span>
              </div>
              <div>
                <span className="text-gray-600">Admission Date:</span>
                <span className="ml-2 font-medium">{data.academic.admissionDate}</span>
              </div>
            </div>
          </div>
        )}

        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center">
            <svg className="w-5 h-5 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <p className="text-yellow-800 text-sm">
              Please review all information carefully. Once submitted, the enrollment will be processed.
            </p>
          </div>
        </div>
      </div>

      <div className="flex justify-between pt-6">
        <button
          type="button"
          onClick={onBack}
          disabled={submitting}
          className="px-6 py-2 text-gray-600 hover:text-gray-800 transition-colors disabled:opacity-50"
        >
          Back
        </button>

        <button
          onClick={onComplete}
          disabled={submitting}
          className="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 flex items-center space-x-2"
        >
          {submitting && (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
          )}
          <span>Submit Enrollment</span>
        </button>
      </div>
    </div>
  );
};

export default ReviewStep;
