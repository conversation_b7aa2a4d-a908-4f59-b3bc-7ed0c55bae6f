// src/repositories/guardianRepository.ts
import { SupabaseClient } from '@supabase/supabase-js';
import {
  DATABASE_COLUMNS,
  DATABASE_TABLES,
  GuardianEntity,
  GuardianInsert,
  GuardianUpdate
} from '../constants/database';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>rror<PERSON><PERSON>ler, ServiceError } from '../services/core/errorHandler';
import { Database } from '../types/database';
import { BaseRepository } from './baseRepository';

/**
 * Guardian-specific repository interface
 */
export interface IGuardianRepository {
  findByStudentId(studentId: string): Promise<GuardianEntity[]>;
  findPrimaryGuardian(studentId: string): Promise<GuardianEntity | null>;
  findByPhone(phone: string): Promise<GuardianEntity[]>;
  isPhoneUnique(phone: string, excludeId?: string): Promise<boolean>;
  setPrimaryGuardian(guardianId: string, studentId: string): Promise<void>;
  findGuardiansWithRelations(studentId: string): Promise<any[]>;
}

/**
 * Guardian repository implementation
 * Handles all database operations related to guardians
 */
export class GuardianRepository extends BaseRepository<GuardianEntity, GuardianInsert, GuardianUpdate> implements IGuardianRepository {
  
  constructor(client: SupabaseClient<Database>) {
    super(client, DATABASE_TABLES.GUARDIANS);
  }

  /**
   * Find all guardians for a specific student
   * @param studentId - Student ID
   * @returns Array of guardian entities
   */
  async findByStudentId(studentId: string): Promise<GuardianEntity[]> {
    try {
      this.log('info', 'Finding guardians by student ID', { studentId });

      const { data, error } = await this.client
        .from(this.tableName)
        .select('*')
        .eq(DATABASE_COLUMNS.GUARDIANS.STUDENT_ID, studentId)
        .eq(DATABASE_COLUMNS.COMMON.IS_ACTIVE, true)
        .order(DATABASE_COLUMNS.GUARDIANS.IS_PRIMARY, { ascending: false });

      if (error) {
        throw ErrorHandler.handle(error, 'Find guardians by student ID');
      }

      this.log('info', 'Guardians found by student ID', { studentId, count: data?.length || 0 });
      return (data || []) as GuardianEntity[];
    } catch (error) {
      this.log('error', 'Error finding guardians by student ID', { error, studentId });
      throw ErrorHandler.handle(error, 'Find guardians by student ID');
    }
  }

  /**
   * Find primary guardian for a student
   * @param studentId - Student ID
   * @returns Primary guardian entity or null
   */
  async findPrimaryGuardian(studentId: string): Promise<GuardianEntity | null> {
    try {
      this.log('info', 'Finding primary guardian', { studentId });

      const guardian = await this.findOne({
        [DATABASE_COLUMNS.GUARDIANS.STUDENT_ID]: studentId,
        [DATABASE_COLUMNS.GUARDIANS.IS_PRIMARY]: true
      });

      this.log('info', 'Primary guardian found', { studentId, found: !!guardian });
      return guardian;
    } catch (error) {
      this.log('error', 'Error finding primary guardian', { error, studentId });
      throw ErrorHandler.handle(error, 'Find primary guardian');
    }
  }

  /**
   * Find guardians by phone number
   * @param phone - Phone number
   * @returns Array of guardian entities
   */
  async findByPhone(phone: string): Promise<GuardianEntity[]> {
    try {
      this.log('info', 'Finding guardians by phone', { phone });

      const { data, error } = await this.client
        .from(this.tableName)
        .select('*')
        .eq(DATABASE_COLUMNS.GUARDIANS.PHONE, phone)
        .eq(DATABASE_COLUMNS.COMMON.IS_ACTIVE, true);

      if (error) {
        throw ErrorHandler.handle(error, 'Find guardians by phone');
      }

      this.log('info', 'Guardians found by phone', { phone, count: data?.length || 0 });
      return (data || []) as GuardianEntity[];
    } catch (error) {
      this.log('error', 'Error finding guardians by phone', { error, phone });
      throw ErrorHandler.handle(error, 'Find guardians by phone');
    }
  }

  /**
   * Check if phone number is unique
   * @param phone - Phone number to check
   * @param excludeId - Guardian ID to exclude from check
   * @returns True if phone is unique
   */
  async isPhoneUnique(phone: string, excludeId?: string): Promise<boolean> {
    try {
      this.log('info', 'Checking phone uniqueness', { phone, excludeId });

      let query = this.client
        .from(this.tableName)
        .select(DATABASE_COLUMNS.COMMON.ID)
        .eq(DATABASE_COLUMNS.GUARDIANS.PHONE, phone)
        .eq(DATABASE_COLUMNS.COMMON.IS_ACTIVE, true);

      if (excludeId) {
        query = query.neq(DATABASE_COLUMNS.COMMON.ID, excludeId);
      }

      const { data, error } = await query.single();

      if (error && error.code !== 'PGRST116') {
        throw ErrorHandler.handle(error, 'Check phone uniqueness');
      }

      const isUnique = data === null;
      this.log('info', 'Phone uniqueness checked', { phone, isUnique });
      return isUnique;
    } catch (error) {
      if (error instanceof ServiceError && error.code === ErrorCode.NOT_FOUND) {
        return true;
      }
      this.log('error', 'Error checking phone uniqueness', { error, phone });
      throw error;
    }
  }

  /**
   * Set a guardian as primary for a student
   * @param guardianId - Guardian ID to set as primary
   * @param studentId - Student ID
   */
  async setPrimaryGuardian(guardianId: string, studentId: string): Promise<void> {
    try {
      this.log('info', 'Setting primary guardian', { guardianId, studentId });

      // First, remove primary status from all guardians for this student
      const { error: removeError } = await this.client
        .from(this.tableName)
        .update({
          [DATABASE_COLUMNS.GUARDIANS.IS_PRIMARY]: false,
          [DATABASE_COLUMNS.COMMON.UPDATED_AT]: new Date().toISOString()
        })
        .eq(DATABASE_COLUMNS.GUARDIANS.STUDENT_ID, studentId)
        .eq(DATABASE_COLUMNS.COMMON.IS_ACTIVE, true);

      if (removeError) {
        throw ErrorHandler.handle(removeError, 'Remove primary status from guardians');
      }

      // Then, set the specified guardian as primary
      const { error: setPrimaryError } = await this.client
        .from(this.tableName)
        .update({
          [DATABASE_COLUMNS.GUARDIANS.IS_PRIMARY]: true,
          [DATABASE_COLUMNS.COMMON.UPDATED_AT]: new Date().toISOString()
        })
        .eq(DATABASE_COLUMNS.COMMON.ID, guardianId)
        .eq(DATABASE_COLUMNS.COMMON.IS_ACTIVE, true);

      if (setPrimaryError) {
        throw ErrorHandler.handle(setPrimaryError, 'Set guardian as primary');
      }

      this.log('info', 'Primary guardian set successfully', { guardianId, studentId });
    } catch (error) {
      this.log('error', 'Error setting primary guardian', { error, guardianId, studentId });
      throw ErrorHandler.handle(error, 'Set primary guardian');
    }
  }

  /**
   * Find guardians with their relation details
   * @param studentId - Student ID
   * @returns Array of guardians with relation information
   */
  async findGuardiansWithRelations(studentId: string): Promise<any[]> {
    try {
      this.log('info', 'Finding guardians with relations', { studentId });

      const { data, error } = await this.client
        .from(this.tableName)
        .select(`
          *,
          relation:${DATABASE_TABLES.GUARDIAN_RELATIONS}(${DATABASE_COLUMNS.GUARDIAN_RELATIONS.ID}, ${DATABASE_COLUMNS.GUARDIAN_RELATIONS.NAME})
        `)
        .eq(DATABASE_COLUMNS.GUARDIANS.STUDENT_ID, studentId)
        .eq(DATABASE_COLUMNS.COMMON.IS_ACTIVE, true)
        .order(DATABASE_COLUMNS.GUARDIANS.IS_PRIMARY, { ascending: false });

      if (error) {
        throw ErrorHandler.handle(error, 'Find guardians with relations');
      }

      this.log('info', 'Guardians with relations found', { studentId, count: data?.length || 0 });
      return data || [];
    } catch (error) {
      this.log('error', 'Error finding guardians with relations', { error, studentId });
      throw ErrorHandler.handle(error, 'Find guardians with relations');
    }
  }

  /**
   * Override create method to add guardian-specific validation
   */
  async create(data: GuardianInsert): Promise<GuardianEntity> {
    try {
      this.log('info', 'Creating guardian', { studentId: data.student_id, name: data.name });

      // Validate required fields
      this.validateEntity(data, [
        'student_id',
        'name',
        'relation_id',
        'phone'
      ]);

      // Validate phone uniqueness
      const isPhoneUnique = await this.isPhoneUnique(data.phone);
      if (!isPhoneUnique) {
        throw new ServiceError(
          ErrorCode.DUPLICATE_VALUE,
          'Phone number already exists',
          null,
          'Create guardian'
        );
      }

      // If this is set as primary, ensure no other primary guardian exists
      if (data.is_primary) {
        const existingPrimary = await this.findPrimaryGuardian(data.student_id);
        if (existingPrimary) {
          throw new ServiceError(
            ErrorCode.BUSINESS_RULE_VIOLATION,
            'Student already has a primary guardian',
            null,
            'Create guardian'
          );
        }
      }

      const guardian = await super.create(data);
      this.log('info', 'Guardian created successfully', { guardianId: guardian.id, studentId: data.student_id });
      return guardian;
    } catch (error) {
      this.log('error', 'Error creating guardian', { error, data });
      throw ErrorHandler.handle(error, 'Create guardian');
    }
  }

  /**
   * Override update method to add guardian-specific validation
   */
  async update(id: string, data: GuardianUpdate): Promise<GuardianEntity> {
    try {
      this.log('info', 'Updating guardian', { guardianId: id });

      // Validate phone uniqueness if being updated
      if (data.phone) {
        const isPhoneUnique = await this.isPhoneUnique(data.phone, id);
        if (!isPhoneUnique) {
          throw new ServiceError(
            ErrorCode.DUPLICATE_VALUE,
            'Phone number already exists',
            null,
            'Update guardian'
          );
        }
      }

      // If setting as primary, ensure no other primary guardian exists
      if (data.is_primary) {
        const guardian = await this.findById(id);
        if (guardian) {
          const existingPrimary = await this.findPrimaryGuardian(guardian.student_id);
          if (existingPrimary && existingPrimary.id !== id) {
            throw new ServiceError(
              ErrorCode.BUSINESS_RULE_VIOLATION,
              'Student already has a primary guardian',
              null,
              'Update guardian'
            );
          }
        }
      }

      const guardian = await super.update(id, data);
      this.log('info', 'Guardian updated successfully', { guardianId: id });
      return guardian;
    } catch (error) {
      this.log('error', 'Error updating guardian', { error, guardianId: id });
      throw ErrorHandler.handle(error, 'Update guardian');
    }
  }
}
