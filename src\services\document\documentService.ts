// src/services/document/documentService.ts
import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '../../types/database';
import { BaseService } from '../core/baseService';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ErrorHandler, ServiceError } from '../core/errorHandler';
import { StorageService, UploadOptions } from './storageService';

/**
 * Document types for student enrollment
 */
export enum DocumentType {
  BIRTH_CERTIFICATE = 'birth_certificate',
  PREVIOUS_RECORDS = 'previous_records',
  MEDICAL_RECORDS = 'medical_records',
  PHOTOGRAPH = 'photograph',
  GUARDIAN_ID = 'guardian_id',
  ADDRESS_PROOF = 'address_proof'
}

/**
 * Document metadata interface
 */
export interface DocumentMetadata {
  id: string;
  studentId: string;
  type: DocumentType;
  fileName: string;
  originalName: string;
  filePath: string;
  fileUrl: string;
  fileSize: number;
  mimeType: string;
  uploadedAt: string;
  uploadedBy?: string;
  isRequired: boolean;
  status: 'pending' | 'approved' | 'rejected';
  notes?: string;
}

/**
 * Document upload request
 */
export interface DocumentUploadRequest {
  studentId: string;
  type: DocumentType;
  file: File;
  isRequired?: boolean;
  notes?: string;
}

/**
 * Student documents collection
 */
export interface StudentDocuments {
  studentId: string;
  documents: DocumentMetadata[];
  requiredDocuments: DocumentType[];
  missingDocuments: DocumentType[];
  completionPercentage: number;
}

/**
 * Document service for managing student documents
 */
export class DocumentService extends BaseService {
  private storageService: StorageService;

  constructor(client: SupabaseClient<Database>) {
    super(client);
    this.storageService = new StorageService(client);
  }

  /**
   * Upload a document for a student
   */
  async uploadDocument(
    request: DocumentUploadRequest
  ): Promise<DocumentMetadata> {
    try {
      this.log('info', 'Starting document upload', {
        studentId: request.studentId,
        type: request.type,
        fileName: request.file.name
      });

      // Validate request
      this.validateUploadRequest(request);

      // Check if student exists
      const studentExists = await this.checkStudentExists(request.studentId);
      if (!studentExists) {
        throw new ServiceError(
          ErrorCode.NOT_FOUND,
          'Student not found',
          null,
          'Document upload'
        );
      }

      // Upload file to storage
      const uploadOptions: UploadOptions = {
        folder: `students/${request.studentId}/documents`,
        fileName: this.generateDocumentFileName(request.type, request.file),
        metadata: {
          studentId: request.studentId,
          documentType: request.type,
          originalName: request.file.name
        }
      };

      const uploadResult = await this.storageService.uploadFile(
        request.file,
        uploadOptions
      );

      // Create document metadata record
      const documentMetadata: DocumentMetadata = {
        id: this.generateId(),
        studentId: request.studentId,
        type: request.type,
        fileName: uploadOptions.fileName!,
        originalName: request.file.name,
        filePath: uploadResult.path,
        fileUrl: uploadResult.url,
        fileSize: uploadResult.size,
        mimeType: uploadResult.type,
        uploadedAt: new Date().toISOString(),
        isRequired: request.isRequired ?? this.isRequiredDocument(request.type),
        status: 'pending',
        notes: request.notes
      };

      // TODO: Store document metadata in database
      // For now, we'll return the metadata object
      // In a real implementation, you'd save this to a documents table

      this.log('info', 'Document upload completed', {
        documentId: documentMetadata.id,
        studentId: request.studentId,
        type: request.type
      });

      return documentMetadata;

    } catch (error) {
      this.log('error', 'Document upload failed', {
        error,
        studentId: request.studentId,
        type: request.type
      });

      // Clean up uploaded file if metadata creation failed
      if (error instanceof ServiceError && error.code !== ErrorCode.FILE_UPLOAD_ERROR) {
        try {
          // Attempt to delete the uploaded file
          // This would require the file path from the upload result
        } catch (cleanupError) {
          this.log('warn', 'Failed to cleanup uploaded file after error', cleanupError);
        }
      }

      throw ErrorHandler.handle(error, 'Upload document');
    }
  }

  /**
   * Upload multiple documents for a student
   */
  async uploadDocuments(
    requests: DocumentUploadRequest[]
  ): Promise<DocumentMetadata[]> {
    const results: DocumentMetadata[] = [];
    const errors: { request: DocumentUploadRequest; error: ServiceError }[] = [];

    for (const request of requests) {
      try {
        const result = await this.uploadDocument(request);
        results.push(result);
      } catch (error) {
        const serviceError = ErrorHandler.handle(error, `Upload document ${request.type}`);
        errors.push({ request, error: serviceError });
      }
    }

    if (errors.length > 0) {
      this.log('warn', 'Some documents failed to upload', { errors });
      
      // If all documents failed, throw error
      if (errors.length === requests.length) {
        throw new ServiceError(
          ErrorCode.FILE_UPLOAD_ERROR,
          `All ${requests.length} documents failed to upload`,
          errors
        );
      }
    }

    return results;
  }

  /**
   * Get all documents for a student
   */
  async getStudentDocuments(studentId: string): Promise<StudentDocuments> {
    try {
      // TODO: Fetch documents from database
      // For now, return mock data structure
      const documents: DocumentMetadata[] = [];
      const requiredDocuments = this.getRequiredDocuments();
      const missingDocuments = requiredDocuments.filter(
        type => !documents.some(doc => doc.type === type)
      );
      const completionPercentage = Math.round(
        ((requiredDocuments.length - missingDocuments.length) / requiredDocuments.length) * 100
      );

      return {
        studentId,
        documents,
        requiredDocuments,
        missingDocuments,
        completionPercentage
      };
    } catch (error) {
      throw ErrorHandler.handle(error, 'Get student documents');
    }
  }

  /**
   * Delete a document
   */
  async deleteDocument(documentId: string): Promise<void> {
    try {
      // TODO: Fetch document metadata from database
      // For now, assume we have the file path
      
      // Delete file from storage
      // await this.storageService.deleteFile(filePath);

      // Delete metadata from database
      // await this.deleteDocumentMetadata(documentId);

      this.log('info', 'Document deleted', { documentId });
    } catch (error) {
      throw ErrorHandler.handle(error, 'Delete document');
    }
  }

  /**
   * Update document status
   */
  async updateDocumentStatus(
    documentId: string,
    status: 'pending' | 'approved' | 'rejected',
    notes?: string
  ): Promise<void> {
    try {
      // TODO: Update document status in database
      this.log('info', 'Document status updated', { documentId, status, notes });
    } catch (error) {
      throw ErrorHandler.handle(error, 'Update document status');
    }
  }

  /**
   * Get document download URL
   */
  async getDocumentUrl(
    documentId: string,
    expiresIn?: number
  ): Promise<string> {
    try {
      // TODO: Fetch document metadata from database to get file path
      const filePath = ''; // Get from database
      
      return await this.storageService.getFileUrl(filePath, undefined, expiresIn);
    } catch (error) {
      throw ErrorHandler.handle(error, 'Get document URL');
    }
  }

  /**
   * Validate document upload request
   */
  private validateUploadRequest(request: DocumentUploadRequest): void {
    this.validateRequired(request, ['studentId', 'type', 'file']);

    if (!Object.values(DocumentType).includes(request.type)) {
      throw new ServiceError(
        ErrorCode.VALIDATION_ERROR,
        `Invalid document type: ${request.type}`
      );
    }

    if (!request.file || request.file.size === 0) {
      throw new ServiceError(
        ErrorCode.VALIDATION_ERROR,
        'File is required and cannot be empty'
      );
    }
  }

  /**
   * Check if student exists
   */
  private async checkStudentExists(studentId: string): Promise<boolean> {
    try {
      const { data, error } = await this.client
        .from('students')
        .select('id')
        .eq('id', studentId)
        .single();

      return !error && data !== null;
    } catch (error) {
      return false;
    }
  }

  /**
   * Generate document file name
   */
  private generateDocumentFileName(type: DocumentType, file: File): string {
    const timestamp = Date.now();
    const extension = file.name.split('.').pop();
    return `${type}_${timestamp}.${extension}`;
  }

  /**
   * Check if document type is required
   */
  private isRequiredDocument(type: DocumentType): boolean {
    const requiredTypes = [
      DocumentType.BIRTH_CERTIFICATE,
      DocumentType.PHOTOGRAPH
    ];
    return requiredTypes.includes(type);
  }

  /**
   * Get list of required document types
   */
  private getRequiredDocuments(): DocumentType[] {
    return [
      DocumentType.BIRTH_CERTIFICATE,
      DocumentType.PHOTOGRAPH
    ];
  }

  /**
   * Clean up documents for a student (used during rollback)
   */
  async cleanupStudentDocuments(studentId: string): Promise<void> {
    try {
      // TODO: Get all documents for student from database
      // Delete files from storage
      // Delete metadata from database
      
      this.log('info', 'Student documents cleaned up', { studentId });
    } catch (error) {
      this.log('error', 'Failed to cleanup student documents', { error, studentId });
      // Don't throw error during cleanup
    }
  }

  /**
   * Validate all required documents are uploaded
   */
  async validateRequiredDocuments(studentId: string): Promise<{
    isComplete: boolean;
    missingDocuments: DocumentType[];
  }> {
    try {
      const studentDocuments = await this.getStudentDocuments(studentId);
      
      return {
        isComplete: studentDocuments.missingDocuments.length === 0,
        missingDocuments: studentDocuments.missingDocuments
      };
    } catch (error) {
      throw ErrorHandler.handle(error, 'Validate required documents');
    }
  }
}
